# .air.toml - Air v1.40+ 配置格式
# 工作目录
# . 或绝对路径，将监视所有文件
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
# Windows: cmd = "go build -o ./tmp/hostaudit.exe ./cmd/api"
cmd = "go build -o ./tmp/hostaudit ./cmd/api"
# Windows：bin = "tmp/hostaudit.exe"
bin = "tmp/hostaudit"
# 自定义执行程序的命令，可以添加额外的编译标识例如添加 GIN_MODE=release
# Windows：full_bin = "./tmp/hostaudit.exe"
full_bin = ""
# 监听以下文件扩展名的文件.
include_ext = ["go", "tpl", "tmpl", "html"]
# 忽略这些文件扩展名或文件.
exclude_file = []
# 监听以下指定目录的文件
include_dir = ["cmd", "internal"]
# 忽略以下指定目录
exclude_dir = []
# 使用正则表达式来忽略文件名
exclude_regex = ["_test.go"]
# 忽略未更改的文件
exclude_unchanged = true
# 是否跟随软连接
follow_symlink = false
# 程序停止后的延迟时间
kill_delay = "0s"
# air的日志文件名，该日志文件放置在你的`tmp_dir`中
log = "build-errors.log"
# 开启polling算法
poll = false
# polling算法的间隔时间
poll_interval = 0
# 重启延时
rerun_delay = 500
# 是否发送中断信号
send_interrupt = true
# 发生构建错误时，停止运行旧的二进制文件。
stop_on_error = true
# 是否重启
rerun = false
# 构建前执行的命令
pre_cmd = []
# 构建后执行的命令
post_cmd = []
# 构建前后命令的工作目录
cmd_dir = ""
# 添加额外的参数到二进制执行文件
args_bin = []

[log]
# 显示日志时间
time = true
# 只显示主要日志
main_only = false
# 不显示日志
silent = false

[color]
# 自定义每个部分显示的颜色。如果找不到颜色，使用原始的应用程序日志。
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# 在退出时删除tmp目录
clean_on_exit = true

[screen]
# 在每次构建时清空终端屏幕
clear_on_rebuild = false
# 在二进制重建完成后继续保持终端滚动位置
keep_scroll = true
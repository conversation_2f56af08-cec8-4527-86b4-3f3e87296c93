# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# moon
.moon/cache
.moon/docker

# env files
.env
.env.*
!.env.example

# output of `go build` and log
.hostaudit
/hostaudit
*.exe
*.exe~
/bin/
/tmp/

# test binary, built with `go test -c`
*.test

# output of the go coverage tool
*.out
coverage.html

# log
logs
*.log

# editor
.vscode/*
!.vscode/settings.json
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# web react
node_modules
/web/dist/**/*
!/web/dist/.gitkeep

# https://moonrepo.dev/docs/config/tasks
$schema: 'https://moonrepo.dev/schemas/tasks.json'

fileGroups:
  api-sources:
    - '**/*.go'
    - 'go.mod'
    - 'go.sum'
    - '!**/*_test.go'
    - '!testdata/**'
    - '!vendor/**'
    - '!bin/**'
    - '!web/**'
  api-tests:
    - '**/*_test.go'
    - 'testdata/**/*'
    - '!vendor/**'
    - '!bin/**'
    - '!web/**'
  api-configs:
    - '**/config.yml'
    - '.env*'
    - '!web/**'

  web-sources:
    - '/web/public/**/*'
    - '/web/src/**/*'
    - '/web/vite.config.ts'

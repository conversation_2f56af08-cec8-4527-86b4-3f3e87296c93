{
  "eslint.enable": true,
  "eslint.format.enable": true,
  "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
  "eslint.useFlatConfig": true,
  // 自动检测 eslint 配置位置：只在 apps/web
  "eslint.workingDirectories": [{ "directory": "apps/web", "changeProcessCWD": true }],
  "eslint.nodePath": "apps/web/node_modules",

  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },

  "files.readonlyInclude": {
    // Tanstack Router generates this file,
    "**/routeTree.gen.ts": true
  },
  "files.watcherExclude": {
    "**/tmp/**": true,
    "**/bin/**": true,
    "**/node_modules/**": true,
    "**/vendor/**": true,
    "**/.git/**": true,
    // Tanstack Router generates this file,
    "**/routeTree.gen.ts": true
  },
  "search.exclude": {
    "**/tmp": true,
    "**/bin": true,
    "**/node_modules": true,
    "**/vendor": true,
    "**/.git": true,
    // Tanstack Router generates this file,
    "**/routeTree.gen.ts": true
  },

  "tailwindCSS.classAttributes": ["className", "\\w*ClassName"],
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(((?:[^()]|\\([^()]*\\))*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(((?:[^()]|\\([^()]*\\))*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["cn\\(((?:[^()]|\\([^()]*\\))*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    ["\\w*ClassName\\s*:\\s*['\"]([^'\"]*)['\"]", "([^'\"]*)"]
  ],

  // Disable auto-imports for MUI components
  // https://mui.com/material-ui/guides/minimizing-bundle-size/#avoid-vs-code-auto-importing-from-barrel-files
  "typescript.preferences.autoImportSpecifierExcludeRegexes": ["^@mui/[^/]+$"],

  // TOML file association
  "evenBetterToml.schema.associations": {
    "\\.air\\.toml$": "https://raw.githubusercontent.com/cosmtrek/air/master/air_example.toml"
  },

  "cSpell.words": [
    "connstring",
    "golangci",
    "hostaudit",
    "mitchellh",
    "natefinch",
    "nolint",
    "pflag",
    "PKCE",
    "readpref",
    "realip",
    "testdata",
    "tomasen",
    "upserted"
  ]
}

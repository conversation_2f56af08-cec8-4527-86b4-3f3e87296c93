# 构建阶段
FROM golang:1.24.3-alpine AS builder

# 安装构建依赖
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 复制 go mod 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
    go build -ldflags="-w -s" -o hostaudit cmd/api/main.go

# 运行阶段
FROM scratch

# 从构建阶段复制时区数据
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
# 从构建阶段复制 CA 证书
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

WORKDIR /app

# 设置时区
ENV TZ=Asia/Shanghai

# 从构建阶段复制可执行文件
COPY --from=builder /app/hostaudit /app/hostaudit

# 复制配置文件示例（可选）
# COPY --from=builder /app/configs/config.example.yml /app/.hostaudit/config.yml

# 暴露端口
EXPOSE 5000

# 运行应用
ENTRYPOINT ["/app/hostaudit"]
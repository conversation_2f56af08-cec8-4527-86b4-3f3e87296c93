services:
  mongo-prod:
    image: mongo:8.0.8
    container_name: hostaudit-mongo-prod
    restart: unless-stopped
    expose:
      - '27017'
    volumes:
      - mongo-data-prod:/data/db
    env_file: .env.prod
    networks:
      - hostaudit-network-prod

  hostaudit-api-prod:
    build:
      context: .
      dockerfile: deployments/docker/Dockerfile
    image: hostaudit-api-prod:latest
    container_name: hostaudit-api-prod
    restart: unless-stopped
    ports:
      - '5000:5000'
    depends_on:
      - mongo-prod
    env_file: .env.prod
    networks:
      - hostaudit-network-prod

volumes:
  mongo-data-prod:
    driver: local
networks:
  hostaudit-network-prod:
    driver: bridge

package handlers

import (
	"encoding/json"
	"net/http"
	"strings"

	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/oauth"
	"jiansec/hostaudit/internal/response"
	"jiansec/hostaudit/internal/storage"

	"github.com/gorilla/sessions"
)

// Handlers 处理器集合
type Handlers struct {
	config       *config.Config
	storage      *storage.Storage
	SessionName  string
	cookieStore  *sessions.CookieStore
	oAuthManager *oauth.OAuthManager
}

// New 创建处理器实例
func New(cfg *config.Config, storage *storage.Storage) *Handlers {
	// 创建 Cookie Store
	cookieStore := sessions.NewCookieStore([]byte(cfg.SessionSecret))
	// 设置 Cookie Store 的安全选项
	cookieStore.Options = &sessions.Options{
		Path:     "/",                  // 全站有效
		MaxAge:   7 * 24 * 3600,        // 7 天
		HttpOnly: true,                 // 防止 XSS
		Secure:   cfg.IsProduction(),   // 生产环境下启用 Secure Cookie
		SameSite: http.SameSiteLaxMode, // 防止 CSRF 攻击
		Domain:   "",                   // 设置域名
	}

	// 创建 OAuthManager
	oAuthManager := oauth.NewOAuthManager(cfg)
	// 注册 Feishu OAuth 提供者
	oAuthManager.RegisterProvider(oauth.NewFeishuProvider(cfg))

	return &Handlers{
		config:       cfg,
		storage:      storage,
		SessionName:  "hostaudit_session",
		cookieStore:  cookieStore,
		oAuthManager: oAuthManager,
	}
}

// respondJSON 发送 JSON 响应
func (h *Handlers) respondJSON(w http.ResponseWriter, status response.ApiStatus, data interface{}, msg ...string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	if err := json.NewEncoder(w).Encode(response.Response{
		Data: data,
		Meta: response.Meta{
			Status: status,
			Msg: func() string {
				if len(msg) > 0 {
					return strings.Join(msg, "\n")
				}
				return response.DefaultApiStatusMessage[status]
			}(),
		},
	}); err != nil {
		h.respondSystemError(w, "Failed to encode response")
	}
}

func (h *Handlers) respondSuccess(w http.ResponseWriter, data interface{}, msg ...string) {
	status := response.StatusSuccess
	h.respondJSON(w, status, data, msg...)
}

func (h *Handlers) respondUnauthorized(w http.ResponseWriter, msg ...string) {
	status := response.StatusUnauthorized
	h.respondJSON(w, status, nil, msg...)
}

func (h *Handlers) respondForbidden(w http.ResponseWriter, msg ...string) {
	status := response.StatusForbidden
	h.respondJSON(w, status, nil, msg...)
}

func (h *Handlers) respondNotFound(w http.ResponseWriter, msg ...string) {
	status := response.StatusNotFound
	h.respondJSON(w, status, nil, msg...)
}

func (h *Handlers) respondError(w http.ResponseWriter, msg ...string) {
	status := response.StatusError
	h.respondJSON(w, status, nil, msg...)
}

func (h *Handlers) respondSystemError(w http.ResponseWriter, msg ...string) {
	status := response.StatusSystemError
	h.respondJSON(w, status, nil, msg...)
}

// Health 健康检查
func (h *Handlers) Health(w http.ResponseWriter, r *http.Request) {
	// 可以在这里添加更多健康检查逻辑，比如检查数据库连接
	h.respondSuccess(w, map[string]interface{}{
		"status": "healthy",
		"env":    h.config.Env,
	})
}

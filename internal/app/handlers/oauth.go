package handlers

import (
	"fmt"
	"log"
	"math/rand"
	"net/http"

	"github.com/go-chi/chi/v5"
	"golang.org/x/oauth2"
)

func (h *Handlers) OAuthLogin(w http.ResponseWriter, r *http.Request) {
	redirectURI := r.URL.Query().Get("redirect_uri")
	if redirectURI == "" {
		log.Println("OAuth Login Error: Missing redirect_uri parameter")
		http.Error(w, "Missing redirect_uri parameter", http.StatusBadRequest)
		return
	}

	providerName := chi.URLParam(r, "provider")
	provider, exists := h.oAuthManager.GetProvider(providerName)
	if !exists {
		log.Printf("OAuth '%s' Login Error: OAuth provider not found", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	session, err := h.cookieStore.Get(r, h.SessionName)
	if err != nil {
		log.Printf("OAuth '%s' Login Error: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	oauthState := fmt.Sprintf("%d", rand.Int63())
	oauthVerifier := oauth2.GenerateVerifier()

	session.Values["oauth_provider"] = providerName
	session.Values["oauth_state"] = oauthState
	session.Values["oauth_verifier"] = oauthVerifier

	// 保存 session
	if err := session.Save(r, w); err != nil {
		log.Printf("OAuth '%s' Login Error: Failed to save session: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}
	// 生成 OAuth 授权 URL
	authURL := provider.GetAuthURL(oauthState, oauthVerifier)
	if authURL == "" {
		log.Printf("OAuth '%s' Login Error: Failed to get auth URL", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	http.Redirect(w, r, authURL, http.StatusFound)
}

func (h *Handlers) OAuthCallback(w http.ResponseWriter, r *http.Request) {
	redirectURI := r.URL.Query().Get("redirect_uri")
	if redirectURI == "" {
		log.Println("OAuth Callback Error: Missing redirect_uri parameter")
		http.Error(w, "Missing redirect_uri parameter", http.StatusBadRequest)
		return
	}

	providerName := chi.URLParam(r, "provider")
	provider, exists := h.oAuthManager.GetProvider(providerName)
	if !exists {
		log.Printf("OAuth '%s' Callback Error: OAuth provider not found", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	session, err := h.cookieStore.Get(r, h.SessionName)
	if err != nil {
		log.Printf("OAuth '%s' Callback Error: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	// 从 session 中获取 state
	state := r.URL.Query().Get("state")
	expectedStateInterface := session.Values["oauth_state"]
	if expectedStateInterface == nil {
		log.Printf("OAuth '%s' Callback Error: Missing expected state in session", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}
	expectedState, ok := expectedStateInterface.(string)
	if !ok {
		log.Printf("OAuth '%s' Callback Error: Expected state is not a string", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	// 如果 state 不匹配，说明是 CSRF 攻击，拒绝处理
	if state != expectedState {
		log.Printf("OAuth '%s' Callback Error: invalid oauth state, expected '%s', got '%s'\n", providerName, expectedState, state)
		delete(session.Values, "oauth_state")
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	code := r.URL.Query().Get("code")
	if code == "" {
		log.Printf("OAuth '%s' Callback Error: Missing code in callback %s", providerName, r.URL.Query().Get("error"))
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	// 从 session 中获取 oauth_verifier
	oauthVerifierInterface := session.Values["oauth_verifier"]
	if oauthVerifierInterface == nil {
		log.Printf("OAuth '%s' Callback Error: Missing oauth_verifier in session", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}
	oauthVerifier, ok := oauthVerifierInterface.(string)
	if !ok {
		log.Printf("OAuth '%s' Callback Error: oauth_verifier is not a string", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}
	user, err := provider.HandleCallback(code, oauthVerifier)
	if err != nil {
		log.Printf("OAuth '%s' Callback Error: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	// 将用户ID存储到 session 中
	session.Values["user_id"] = user.ID
	// 清理 OAuth 相关的 session 值
	delete(session.Values, "oauth_provider")
	delete(session.Values, "oauth_state")
	delete(session.Values, "oauth_verifier")

	// 保存 session
	if err := session.Save(r, w); err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to save session: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	log.Printf("OAuth '%s' Callback Success: User %s logged in successfully", providerName, user.ID)
	// TODO: 注册用户 或者 更新用户信息 并且签发 JWT Token
	// 1. 程序第一次启动时，创建超级管理员用户
	// 2. 用户登录时，检查用户是否存在，如果不存在则创建，否则更新用户信息
	// 3. 签发 JWT Token
	http.Redirect(w, r, redirectURI, http.StatusFound)
}

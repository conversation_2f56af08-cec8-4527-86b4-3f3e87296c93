package server

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/pkg/logger"
	"jiansec/hostaudit/internal/storage"
	"jiansec/hostaudit/internal/storage/mongo"
)

// Run 启动服务器
func Run() error {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// 设置日志
	logger.Setup(cfg)

	// 初始化存储
	store, cleanup, err := mongo.NewStorage(cfg.MongoURI)
	if err != nil {
		return fmt.Errorf("failed to initialize storage: %w", err)
	}
	defer cleanup()

	// 确保配置存储已初始化
	err = store.Config.Save(context.Background(), cfg)
	if err != nil {
		return fmt.Errorf("failed to save initial config: %w", err)
	}

	// 创建 HTTP 服务器
	srv := NewHTTPServer(cfg, store)

	// 启动服务器并处理优雅关闭
	return gracefulShutdown(srv, cfg)
}

// gracefulShutdown 优雅关闭服务器
func gracefulShutdown(srv *http.Server, cfg *config.Config) error {
	// 创建一个通道来监听中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 在 goroutine 中启动服务器
	serverErr := make(chan error, 1)
	go func() {
		log.Printf("Starting server at %s:%s\n", cfg.ServerAddr, cfg.ServerPort)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			serverErr <- fmt.Errorf("failed to start server: %w", err)
		}
	}()

	// 等待中断信号或服务器错误
	select {
	case err := <-serverErr:
		return err
	case <-quit:
		log.Println("Shutting down server...")
	}

	// 创建一个 deadline 来等待正在处理的请求完成
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := srv.Shutdown(ctx); err != nil {
		return fmt.Errorf("server forced to shutdown: %w", err)
	}

	log.Println("Server exited gracefully")
	return nil
}

// NewHTTPServer 创建 HTTP 服务器
func NewHTTPServer(cfg *config.Config, store *storage.Storage) *http.Server {
	router := NewRouter(cfg, store)

	addr := net.JoinHostPort(cfg.ServerAddr, cfg.ServerPort)

	return &http.Server{
		Addr:         addr,
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
}

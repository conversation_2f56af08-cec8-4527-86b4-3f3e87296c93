package config

import (
	"errors"
	"fmt"
	"os"
)

// Config 应用配置
type Config struct {
	FirstRun      bool   `bson:"-" json:"-"`                       // 是否第一次运行
	Env           string `bson:"env" json:"env"`                   // 环境变量
	MongoURI      string `bson:"-" json:"-"`                       // MongoDB URI（敏感信息）
	SessionSecret string `bson:"-" json:"-"`                       // 会话密钥（敏感信息）
	JWTSecret     string `bson:"-" json:"-"`                       // JWT 密钥（敏感信息）
	ServerAddr    string `bson:"server_addr" json:"server_addr"`   // 服务器地址
	ServerPort    string `bson:"server_port" json:"server_port"`   // 服务器端口
	ServerLog     string `bson:"server_log" json:"server_log"`     // 日志输出
	JWTExpiry     string `bson:"jwt_expiry" json:"jwt_expiry"`     // JWT 过期时间
	OAuthFeishu   OAuth  `bson:"oauth_feishu" json:"oauth_feishu"` // 飞书 OAuth 配置
}

// OAuth OAuth 配置
type OAuth struct {
	ClientID     string `bson:"client_id" json:"client_id"`
	ClientSecret string `bson:"-" json:"-"`
	RedirectURI  string `bson:"redirect_uri" json:"redirect_uri"`
}

// 默认配置值
const (
	DefaultEnv        = "production"
	DefaultServerAddr = "" // 默认监听所有接口
	DefaultServerPort = "5000"
	DefaultServerLog  = "stdout" // stdout, stderr, 或文件路径
	DefaultJWTExpiry  = "604800" // 7 天（秒）
)

// Validate 验证配置
func (c *Config) Validate() error {
	if c.MongoURI == "" {
		return errors.New("environment variable HOSTAUDIT_MONGO_URI is required")
	}

	if c.SessionSecret == "" {
		return errors.New("environment variable HOSTAUDIT_SESSION_SECRET is required")
	}

	if c.JWTSecret == "" {
		return errors.New("environment variable HOSTAUDIT_JWT_SECRET is required")
	}

	if c.OAuthFeishu.ClientID == "" {
		return errors.New("environment variable HOSTAUDIT_OAUTH_FEISHU_CLIENT_ID is required")
	}

	if c.OAuthFeishu.ClientSecret == "" {
		return errors.New("environment variable HOSTAUDIT_OAUTH_FEISHU_CLIENT_SECRET is required")
	}

	if c.OAuthFeishu.RedirectURI == "" {
		return errors.New("environment variable HOSTAUDIT_OAUTH_FEISHU_REDIRECT_URI is required")
	}

	// 验证环境变量
	switch c.Env {
	case "development", "staging", "production":
		// 有效的环境
	default:
		return fmt.Errorf("invalid environment: %s", c.Env)
	}

	// 验证日志输出
	switch c.ServerLog {
	case "stdout", "stderr", "":
		// 有效的输出
	default:
		// 假设是文件路径，验证是否可写
		if err := isWritable(c.ServerLog); err != nil {
			return fmt.Errorf("log file not writable: %w", err)
		}
	}

	return nil
}

// IsDevelopment 是否为开发环境
func (c *Config) IsDevelopment() bool {
	return c.Env == "development"
}

// IsProduction 是否为生产环境
func (c *Config) IsProduction() bool {
	return c.Env == "production"
}

// isWritable 检查指定路径的文件是否可写（如果文件不存在则尝试创建）
func isWritable(path string) error {
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
	if err != nil {
		return err
	}
	return f.Close()
}

package config

import "context"

// StorageBackend 配置存储后端接口
type StorageBackend interface {
	Get(ctx context.Context) (*Config, error)
	Save(ctx context.Context, cfg *Config) error
	GetCached(ctx context.Context) (*Config, error)
	ClearCache()
}

// Storage 配置存储
type Storage struct {
	backend StorageBackend
}

// NewStorage 创建新的配置存储实例
func NewStorage(backend StorageBackend) *Storage {
	return &Storage{
		backend: backend,
	}
}

// Get 获取配置
func (s *Storage) Get(ctx context.Context) (*Config, error) {
	return s.backend.Get(ctx)
}

// Save 保存配置
func (s *Storage) Save(ctx context.Context, cfg *Config) error {
	// 清除缓存以确保下次获取最新数据
	s.backend.ClearCache()
	return s.backend.Save(ctx, cfg)
}

// GetCached 获取缓存的配置
func (s *Storage) GetCached(ctx context.Context) (*Config, error) {
	return s.backend.GetCached(ctx)
}

// ClearCache 清除缓存
func (s *Storage) ClearCache() {
	s.backend.ClearCache()
}

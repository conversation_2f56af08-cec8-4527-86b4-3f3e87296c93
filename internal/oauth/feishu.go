package oauth

import (
	"context"
	"encoding/json"
	"fmt"
	"io"

	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/users"

	"golang.org/x/oauth2"
)

const (
	ProviderNameFeishu = "feishu"
)

type FeishuProvider struct {
	Config *oauth2.Config
}

// 飞书 OAuth2 Endpoint
var FeishuEndpoint = oauth2.Endpoint{
	AuthURL:  "https://accounts.feishu.cn/open-apis/authen/v1/authorize",
	TokenURL: "https://open.feishu.cn/open-apis/authen/v2/oauth/token",
}

func NewFeishuProvider(cfg *config.Config) *FeishuProvider {
	return &FeishuProvider{
		Config: &oauth2.Config{
			ClientID:     cfg.OAuthFeishu.ClientID,
			ClientSecret: cfg.OAuthFeishu.ClientSecret,
			RedirectURL:  cfg.OAuthFeishu.RedirectURI,
			Endpoint:     FeishuEndpoint,
		},
	}
}

func (f *FeishuProvider) GetName() string {
	return ProviderNameFeishu
}

func (f *FeishuProvider) GetAuthURL(state, verifier string) string {
	authURL := f.Config.AuthCodeURL(state, oauth2.S256ChallengeOption(verifier))
	return authURL
}

// 飞书的回调处理 - 使用标准OAuth2流程 + PKCE
func (f *FeishuProvider) HandleCallback(code, verifier string) (*users.User, error) {
	token, err := f.Config.Exchange(context.Background(), code, oauth2.VerifierOption(verifier))
	if err != nil {
		return nil, fmt.Errorf("交换飞书令牌失败: %w", err)
	}

	// 获取用户信息
	return f.getUserInfo(token)
}

func (f *FeishuProvider) getUserInfo(token *oauth2.Token) (*users.User, error) {
	client := f.Config.Client(context.Background(), token)
	resp, err := client.Get("https://open.feishu.cn/open-apis/authen/v1/user_info")
	if err != nil {
		return nil, fmt.Errorf("获取飞书用户信息失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var feishuResp struct {
		Code int    `json:"code"` // 错误码，非 0 表示失败
		Msg  string `json:"msg"`  // 错误描述
		Data struct {
			Name            string `json:"name"`             // 用户姓名
			EnName          string `json:"en_name"`          // 用户英文名称
			AvatarURL       string `json:"avatar_url"`       // 用户头像
			AvatarThumb     string `json:"avatar_thumb"`     // 用户头像 72x72
			AvatarMiddle    string `json:"avatar_middle"`    // 用户头像 240x240
			AvatarBig       string `json:"avatar_big"`       // 用户头像 640x640
			OpenID          string `json:"open_id"`          // 用户在应用内的唯一标识
			UnionID         string `json:"union_id"`         // 用户对ISV的唯一标识，对于同一个ISV，用户在其名下所有应用的union_id相同
			UserID          string `json:"user_id"`          // 用户 user_id
			Mobile          string `json:"mobile"`           // 用户手机号
			Email           string `json:"email"`            // 用户邮箱
			EmployeeNo      string `json:"employee_no"`      // 用户工号
			TenantKey       string `json:"tenant_key"`       // 当前企业标识
			EnterpriseEmail string `json:"enterprise_email"` // 企业邮箱
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &feishuResp); err != nil {
		return nil, fmt.Errorf("解析飞书用户信息失败: %w", err)
	}

	if feishuResp.Code != 0 {
		return nil, fmt.Errorf("飞书API错误: %s", feishuResp.Msg)
	}

	return &users.User{
		ID:        feishuResp.Data.UserID,
		Username:  feishuResp.Data.EnName,
		Nickname:  feishuResp.Data.Name,
		Email:     feishuResp.Data.Email,
		AvatarURL: feishuResp.Data.AvatarURL,
		Provider:  "feishu",
		RawData: map[string]interface{}{
			"avatar_thumb":     feishuResp.Data.AvatarThumb,
			"avatar_middle":    feishuResp.Data.AvatarMiddle,
			"avatar_big":       feishuResp.Data.AvatarBig,
			"open_id":          feishuResp.Data.OpenID,
			"union_id":         feishuResp.Data.UnionID,
			"mobile":           feishuResp.Data.Mobile,
			"email":            feishuResp.Data.Email,
			"employee_no":      feishuResp.Data.EmployeeNo,
			"tenant_key":       feishuResp.Data.TenantKey,
			"enterprise_email": feishuResp.Data.EnterpriseEmail,
		},
	}, nil
}

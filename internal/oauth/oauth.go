package oauth

import (
	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/users"
)

// OAuth Provider 接口 - 统一标准OAuth2实现
type OAuthProvider interface {
	GetAuthURL(state, verifier string) string                  // state用于防止CSRF攻击, verifier用于PKCE
	HandleCallback(code, verifier string) (*users.User, error) // code是OAuth2授权回调中的授权码, verifier用于PKCE验证
	GetName() string                                           // GetName 返回OAuth提供者的名称，例如 "github", "google", "feishu" 等
}

// OAuthManager 结构体 - 管理多个OAuth提供者
type OAuthManager struct {
	providers map[string]OAuthProvider // 存储所有已注册的OAuth提供者
}

func NewOAuthManager(cfg *config.Config) *OAuthManager {
	return &OAuthManager{
		providers: make(map[string]OAuthProvider),
	}
}

func (om *OAuthManager) RegisterProvider(provider OAuthProvider) {
	om.providers[provider.GetName()] = provider
}

func (om *OAuthManager) GetProvider(name string) (OAuthProvider, bool) {
	provider, exists := om.providers[name]
	return provider, exists
}

func (om *OAuthManager) GetProviders() map[string]OAuthProvider {
	return om.providers
}

package utils

import (
	"errors"
	"fmt"
	"net/url"
	"strings"

	"go.mongodb.org/mongo-driver/x/mongo/driver/connstring"
)

// ExtractDBName 从 MongoDB URI 中提取数据库名称
func ExtractDBName(mongoURI string) (string, error) {
	if mongoURI == "" {
		return "", errors.New("MongoDB URI cannot be empty")
	}

	// 解析连接字符串
	cs, err := connstring.Parse(mongoURI)
	if err != nil {
		return "", fmt.Errorf("failed to parse MongoDB URI: %w", err)
	}

	// 检查数据库名称
	if cs.Database == "" {
		return "", errors.New("database name is required in MongoDB URI")
	}

	return cs.Database, nil
}

// ValidateURI 验证 MongoDB URI
func ValidateURI(mongoURI string) error {
	if mongoURI == "" {
		return errors.New("MongoDB URI cannot be empty")
	}

	// 尝试解析 URI
	_, err := connstring.Parse(mongoURI)
	if err != nil {
		return fmt.Errorf("invalid MongoDB URI: %w", err)
	}

	return nil
}

// SanitizeURI 清理 MongoDB URI，移除敏感信息用于日志
func SanitizeURI(mongoURI string) string {
	u, err := url.Parse(mongoURI)
	if err != nil {
		// 如果解析失败，返回占位符
		return "mongodb://***"
	}

	// 移除用户信息
	if u.User != nil {
		u.User = url.UserPassword("***", "***")
	}

	return u.String()
}

// BuildURI 构建 MongoDB URI
func BuildURI(host string, port string, database string, username string, password string) string {
	var uri strings.Builder

	uri.WriteString("mongodb://")

	// 添加认证信息
	if username != "" && password != "" {
		uri.WriteString(url.QueryEscape(username))
		uri.WriteString(":")
		uri.WriteString(url.QueryEscape(password))
		uri.WriteString("@")
	}

	// 添加主机和端口
	uri.WriteString(host)
	if port != "" {
		uri.WriteString(":")
		uri.WriteString(port)
	}

	// 添加数据库
	if database != "" {
		uri.WriteString("/")
		uri.WriteString(database)
	}

	return uri.String()
}

package utils

import (
	"testing"
)

func TestExtractDBName(t *testing.T) {
	tests := []struct {
		name    string
		uri     string
		want    string
		wantErr bool
	}{
		{"valid", "mongodb://localhost:27017/dbname", "dbname", false},
		{"withCreds", "******************************************", "dbname", false},
		{"empty", "", "", true},
		{"missingDB", "mongodb://localhost:27017", "", true},
		{"malformed", "mongodb://%41:27017", "", true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractDBName(tt.uri)
			if (err != nil) != tt.wantErr {
				t.Fatalf("expected error=%v got %v", tt.wantErr, err)
			}
			if err == nil && got != tt.want {
				t.Fatalf("expected %s got %s", tt.want, got)
			}
		})
	}
}

func TestValidateURI(t *testing.T) {
	tests := []struct {
		name    string
		uri     string
		wantErr bool
	}{
		{"valid", "mongodb://localhost:27017/dbname", false},
		{"withCreds", "******************************************", false},
		{"empty", "", true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateURI(tt.uri)
			if (err != nil) != tt.wantErr {
				t.Fatalf("expected error=%v got %v", tt.wantErr, err)
			}
		})
	}
}

func TestSanitizeURI(t *testing.T) {
	sanitized := SanitizeURI("******************************************")
	expected := "****************************************************"
	if sanitized != expected {
		t.Fatalf("expected %s got %s", expected, sanitized)
	}

	invalid := SanitizeURI(":::invalid")
	if invalid != "mongodb://***" {
		t.Fatalf("expected placeholder for invalid URI got %s", invalid)
	}
}

func TestBuildURI(t *testing.T) {
	withCreds := BuildURI("localhost", "27017", "dbname", "user", "pass")
	expectedCreds := "******************************************"
	if withCreds != expectedCreds {
		t.Fatalf("expected %s got %s", expectedCreds, withCreds)
	}

	noCreds := BuildURI("localhost", "27017", "dbname", "", "")
	expectedNoCreds := "mongodb://localhost:27017/dbname"
	if noCreds != expectedNoCreds {
		t.Fatalf("expected %s got %s", expectedNoCreds, noCreds)
	}
}

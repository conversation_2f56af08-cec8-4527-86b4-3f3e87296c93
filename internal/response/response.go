package response

// ApiStatus 定义API状态码
type ApiStatus int

const (
	StatusSuccess      ApiStatus = 2000 // 成功
	StatusUnauthorized ApiStatus = 401  // 未授权
	StatusForbidden    ApiStatus = 403  // 禁止访问
	StatusNotFound     ApiStatus = 404  // 资源不存在
	StatusError        ApiStatus = 200  // 请求失败
	StatusExpiration   ApiStatus = 600  // 登录过期
	StatusSystemError  ApiStatus = 500  // 系统错误
)

// DefaultApiStatusMessage 状态码描述
var DefaultApiStatusMessage = map[ApiStatus]string{
	StatusSuccess:      "",
	StatusUnauthorized: "未授权，请登录",
	StatusForbidden:    "禁止访问该资源",
	StatusNotFound:     "资源不存在",
	StatusError:        "请求失败",
	StatusExpiration:   "登录过期",
	StatusSystemError:  "系统错误",
}

// Response 定义统一的响应结构
type Response struct {
	Data interface{} `json:"data"`
	Meta Meta        `json:"meta"`
}

// Meta 元数据
type Meta struct {
	Status ApiStatus `json:"status"`
	Msg    string    `json:"msg"`
}

package mongo

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
)

// IndexManager 索引管理器
type IndexManager struct {
	db *mongo.Database
}

// NewIndexManager 创建索引管理器
func NewIndexManager(db *mongo.Database) *IndexManager {
	return &IndexManager{db: db}
}

// EnsureIndexes 确保所有索引都已创建
func (im *IndexManager) EnsureIndexes(ctx context.Context) error {
	// 为每个集合创建索引
	if err := im.createConfigIndexes(ctx); err != nil {
		return fmt.Errorf("failed to create config indexes: %w", err)
	}

	return nil
}

// createConfigIndexes 创建配置集合的索引
func (im *IndexManager) createConfigIndexes(ctx context.Context) error {
	collection := im.db.Collection(CollectionConfig)

	indexes := []mongo.IndexModel{}

	return im.createIndexes(ctx, collection, indexes)
}

// createIndexes 创建索引的通用方法
func (im *IndexManager) createIndexes(ctx context.Context, collection *mongo.Collection, indexes []mongo.IndexModel) error {
	if len(indexes) == 0 {
		return nil
	}

	// 设置索引创建超时
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	indexView := collection.Indexes()

	// 创建索引
	names, err := indexView.CreateMany(ctx, indexes)
	if err != nil {
		return fmt.Errorf("failed to create indexes for collection %s: %w", collection.Name(), err)
	}

	log.Printf("Created indexes for collection %s: %v\n", collection.Name(), names)
	return nil
}

// DropAllIndexes 删除所有索引（仅用于开发/测试）
func (im *IndexManager) DropAllIndexes(ctx context.Context, collections []string) error {
	for _, collName := range collections {
		collection := im.db.Collection(collName)
		if _, err := collection.Indexes().DropAll(ctx); err != nil {
			return fmt.Errorf("failed to drop indexes for collection %s: %w", collName, err)
		}
		log.Printf("Dropped all indexes for collection %s\n", collName)
	}

	return nil
}

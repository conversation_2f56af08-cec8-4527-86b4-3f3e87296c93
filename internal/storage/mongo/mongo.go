package mongo

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/pkg/utils"
	"jiansec/hostaudit/internal/storage"
)

// NewStorage 创建 MongoDB 存储实例
func NewStorage(mongoURI string) (*storage.Storage, func(), error) {
	// 提取数据库名称
	dbName, err := utils.ExtractDBName(mongoURI)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to extract database name: %w", err)
	}

	// 创建客户端选项
	clientOptions := options.Client().
		ApplyURI(mongoURI).
		SetConnectTimeout(10 * time.Second).
		SetServerSelectionTimeout(5 * time.Second)

	// 连接到 MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// Ping 测试连接
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		client.Disconnect(ctx)
		return nil, nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}

	// 获取数据库
	db := client.Database(dbName)
	// 创建索引管理器
	indexManager := NewIndexManager(db)
	// 确保索引存在
	if err := indexManager.EnsureIndexes(ctx); err != nil {
		client.Disconnect(ctx)
		return nil, nil, fmt.Errorf("failed to ensure indexes: %w", err)
	}

	// 创建配置存储
	configBackend := newConfigBackend(db)
	configStore := config.NewStorage(configBackend)

	// 创建存储聚合
	store := &storage.Storage{
		Config: configStore,
	}

	// 清理函数
	cleanup := func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := client.Disconnect(ctx); err != nil {
			// 仅记录错误，不返回
			log.Printf("Error disconnecting from MongoDB: %v\n", err)
		}
	}

	return store, cleanup, nil
}

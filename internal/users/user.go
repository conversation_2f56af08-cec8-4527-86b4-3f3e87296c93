package users

import "time"

type Role string

const (
	// RoleSuperAdmin 超级管理员角色
	RoleSuperAdmin Role = "super_admin"
	// RoleAdmin 管理员角色
	RoleAdmin Role = "admin"
	// RoleUser 普通用户角色
	RoleUser Role = "user"
)

type User struct {
	ID        string                 `bson:"id" json:"id"`
	Username  string                 `bson:"username" json:"username"`     // 用户名用于登录
	Nickname  string                 `bson:"nickname" json:"nickname"`     // 昵称中文名
	Email     string                 `bson:"email" json:"email"`           // 邮箱地址
	AvatarURL string                 `bson:"avatar_url" json:"avatar_url"` // 头像URL
	Role      Role                   `bson:"role" json:"role"`             // 角色
	Password  string                 `bson:"password" json:"-"`            // 不在JSON响应中返回密码
	Salt      string                 `bson:"salt" json:"-"`                // 不在JSON响应中返回盐值
	Provider  string                 `json:"provider"`                     // e.g., "feishu", "google"
	RawData   map[string]interface{} `json:"raw_data,omitempty"`           // Raw data from the provider
	CreatedAt time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time              `bson:"updated_at" json:"updated_at"`
}

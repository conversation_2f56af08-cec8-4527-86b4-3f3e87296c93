# https://moonrepo.dev/docs/config/project
$schema: 'https://moonrepo.dev/schemas/project.json'

language: 'go'
type: 'application'
stack: 'backend'

tags:
  - 'backend'
  - 'golang'
  - 'api'

tasks:
  # 安装依赖
  install:
    command: 'go mod download'
    inputs:
      - 'go.mod'
      - 'go.sum'

  dev:
    command: 'air'
    preset: 'server'
    deps:
      - '~:install'
    inputs:
      - '@group(api-sources)'
      - '@group(api-configs)'
      - '.air.toml'

  build:
    command: 'go build -o bin/hostaudit cmd/api/main.go'
    inputs:
      - '@globs(api-sources)'

    outputs:
      - 'bin/hostaudit'
    deps:
      - 'install'

  build-all:
    command: 'scripts/build.sh'
    inputs:
      - '@globs(api-sources)'
      - '@globs(api-configs)'
    outputs:
      - 'bin/hostaudit'
      - 'bin/hostaudit-linux-amd64'
      - 'bin/hostaudit-linux-arm64'
      - 'bin/hostaudit-darwin-amd64'
      - 'bin/hostaudit-darwin-arm64'
      - 'bin/hostaudit-windows-amd64.exe'
    deps:
      - '~:install'
      - '~:clean'

  start:
    command: 'bin/hostaudit'
    preset: 'watcher'
    inputs:
      - '@group(api-configs)'
    deps:
      - '~:build'
    options:
      envFile: '.env'

  test:
    command: 'go test -v ./...'
    deps:
      - '~:install'
    inputs:
      - '@group(api-sources)'
      - '@group(api-tests)'

  fmt:
    command: 'go fmt ./...'
    inputs:
      - '@group(api-sources)'
      - '@group(api-tests)'

  # 清理
  clean:
    script: 'rm -rf ./bin coverage.out coverage.html'
    options:
      cache: false

  docker-dev:
    script: 'docker compose -f ./docker-compose.dev.yml up --build -d'
    inputs:
      - '@group(api-sources)'
      - '@group(api-configs)'
    deps:
      - '~:install'

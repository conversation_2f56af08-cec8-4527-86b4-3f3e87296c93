#!/bin/bash

# Host Audit 构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的信息
info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 获取版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S')
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

info "Building Host Audit..."
info "Version: $VERSION"
info "Commit: $GIT_COMMIT"
info "Build Time: $BUILD_TIME"

# 设置构建变量
LDFLAGS="-X 'main.Version=$VERSION' -X 'main.BuildTime=$BUILD_TIME' -X 'main.GitCommit=$GIT_COMMIT'"

# 清理旧的构建
if [ -d "bin" ]; then
    info "Cleaning old builds..."
    rm -rf bin
fi

mkdir -p bin

# 构建不同平台的二进制文件
PLATFORMS=("linux/amd64" "linux/arm64" "darwin/amd64" "darwin/arm64" "windows/amd64")
GO_BIN=$(which go)

for platform in "${PLATFORMS[@]}"; do
    platform_split=(${platform//\// })
    GOOS=${platform_split[0]}
    GOARCH=${platform_split[1]}
    
    output_name="hostaudit-$GOOS-$GOARCH"
    if [ $GOOS = "windows" ]; then
        output_name+='.exe'
    fi
    
    info "Building for $GOOS/$GOARCH..."
    
    env GOOS=$GOOS GOARCH=$GOARCH CGO_ENABLED=0 \
        $GO_BIN build -ldflags="$LDFLAGS -w -s" \
        -o bin/$output_name \
        cmd/api/main.go
    
    if [ $? -eq 0 ]; then
        info "✓ Built: bin/$output_name"
    else
        error "✗ Failed to build for $GOOS/$GOARCH"
        exit 1
    fi
done

# 创建当前平台的符号链接
CURRENT_OS=$(go env GOOS)
CURRENT_ARCH=$(go env GOARCH)
CURRENT_BINARY="hostaudit-$CURRENT_OS-$CURRENT_ARCH"
if [ $CURRENT_OS = "windows" ]; then
    CURRENT_BINARY+='.exe'
fi

if [ -f "bin/$CURRENT_BINARY" ]; then
    info "Creating symlink for current platform..."
    cd bin
    ln -sf $CURRENT_BINARY hostaudit
    cd ..
fi

info "Build completed successfully!"
info "Binaries are in the 'bin' directory"
info "You can run the server with: ./bin/hostaudit"
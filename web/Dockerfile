# Stage 1: Build the web application
FROM node:22.14.0-slim AS web-base

# Set working directory
WORKDIR /app

# Install moon CLI globally
RUN npm install -g @moonrepo/cli

# Install necessary packages for building
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Stage 2: Scaffold the workspace
FROM web-base AS web-skeleton

# Copy the entire repository for scaffolding
COPY . .

# Scaffold the web project
RUN moon docker scaffold web

# Stage 3: Build the web application
FROM web-base AS web-build

# Copy proto toolchain
COPY --from=web-skeleton /root/.proto /root/.proto

# Copy workspace skeleton
COPY --from=web-skeleton /app/.moon/docker/workspace .

# Install toolchain and dependencies
RUN moon docker setup

# Copy source files
COPY --from=web-skeleton /app/.moon/docker/sources .

# Build the web application
RUN moon run web:build

# Prune workspace
RUN moon docker prune

# Stage 4: Final image with Nginx
FROM nginx:stable-alpine

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates

# Copy Nginx configuration
COPY apps/web/nginx/default.conf /etc/nginx/conf.d/default.conf

# Copy web build output to Nginx html directory
COPY --from=web-build /app/apps/web/dist /usr/share/nginx/html

# Expose port 80 for Nginx
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

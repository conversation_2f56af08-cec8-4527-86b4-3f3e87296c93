# https://moonrepo.dev/docs/config/project
$schema: 'https://moonrepo.dev/schemas/project.json'

type: 'application'
language: 'typescript'
stack: 'frontend'

tasks:
  dev: 
    command: 'vite'
    preset: 'watcher'
    inputs:
      - '@globs(web-sources)'

  preview:
    command: 'vite preview'
    preset: 'watcher'
    deps:
      - '~:build'

  build:
    command: 'vite build'
    inputs:
      - '@globs(web-sources)'
    outputs:
      - 'dist'
    deps:
      - '~:lint'
      - '~:type-check'
      - '^:build'

  lint:
    command: 'eslint --no-warn-ignored src'
    inputs:
      - '@globs(web-sources)'
      - 'eslint.config.js'

  type-check:
    command: 'tsc -b --noEmit'
    inputs:
      - '@globs(web-sources)'

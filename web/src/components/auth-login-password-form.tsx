import Box from '@mui/material/Box'
import InputLabel from '@mui/material/InputLabel'
import OutlinedInput from '@mui/material/OutlinedInput'
import Stack from '@mui/material/Stack'
import { useNavigate } from '@tanstack/react-router'

import { ROUTE_PATHS } from '@/constants/routes'
import { useAuth } from '@/contexts/auth'
import {
  type LoginWithPasswordRequestData,
  LoginWithPasswordRequestDataSchema,
  useLoginWithPasswordMutation
} from '@/hooks/api/auth'
import { useAppForm } from '@/hooks/form'
import { resolveFormErrorMessage } from '@/lib/form'
import { PasswordInputWithEye } from './design-systems/password-input-with-eye'

const defaultValues: LoginWithPasswordRequestData = {
  username: '',
  password: ''
}

export const AuthLoginPasswordForm = () => {
  const { mutateAsync: loginWithPassword } = useLoginWithPasswordMutation()
  const { login } = useAuth()
  const navigate = useNavigate()

  const form = useAppForm({
    defaultValues,
    validators: {
      onChange: LoginWithPasswordRequestDataSchema,
      onSubmitAsync: async ({ value }) => {
        const { username, password } = value
        try {
          const { data } = await loginWithPassword({ username, password })
          login(data.token)
          navigate({
            to: ROUTE_PATHS.INDEX.path,
            from: ROUTE_PATHS.AUTH_LOGIN_PASSWORD.path,
            replace: true
          })
        } catch (error) {
          return resolveFormErrorMessage(error)
        }
      }
    }
  })

  return (
    <form
      onSubmit={e => {
        e.preventDefault()
        e.stopPropagation()
        form.handleSubmit()
      }}
      noValidate
    >
      <form.AppForm>
        <Stack gap={2}>
          <form.AppField
            name='username'
            children={field => (
              <Box>
                <InputLabel htmlFor={field.name}>用户名</InputLabel>
                <OutlinedInput
                  id={field.name}
                  name={field.name}
                  defaultValue={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  error={field.state.meta.isTouched && !field.state.meta.isValid}
                  placeholder='请输入用户名'
                  autoComplete='username'
                  fullWidth
                />
                <field.ErrorMessage />
              </Box>
            )}
          />

          <form.AppField
            name='password'
            children={field => (
              <Box>
                <InputLabel htmlFor={field.name}>密码</InputLabel>
                <PasswordInputWithEye
                  id={field.name}
                  name={field.name}
                  defaultValue={field.state.value}
                  onChange={e => field.handleChange(e.target.value)}
                  error={field.state.meta.isTouched && !field.state.meta.isValid}
                  placeholder='请输入密码'
                  autoComplete='current-password'
                />
                <field.ErrorMessage />
              </Box>
            )}
          />
        </Stack>

        <form.SubmitButton>登录</form.SubmitButton>

        <form.SubmitErrorMessage sx={{ mt: 2 }} />
      </form.AppForm>
    </form>
  )
}

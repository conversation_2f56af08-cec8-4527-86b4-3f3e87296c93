import * as React from 'react'
import MuiBreadcrumbs from '@mui/material/Breadcrumbs'
import { useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import { IconChevronRight } from '@tabler/icons-react'
import { Link, useLocation } from '@tanstack/react-router'

import { APP_DEFAULT_PATH } from '@/config'
import { menuItems } from '@/menu'
import { generateFocusStyle } from '@/theme/utils/generate-focus-style'

interface BreadcrumbItem {
  title: string
  url?: string
  children?: BreadcrumbItem[]
}

interface BreadcrumbsProps {
  data?: BreadcrumbItem[]
}

const homeBreadcrumb: BreadcrumbItem = { title: '首页', url: APP_DEFAULT_PATH }

export const Breadcrumbs = ({ data }: BreadcrumbsProps) => {
  const theme = useTheme()
  const location = useLocation()
  const pathname = location.pathname

  const [breadcrumbItems, setBreadcrumbItems] = React.useState<BreadcrumbItem[]>([])
  const [activeItem, setActiveItem] = React.useState<BreadcrumbItem>()

  React.useEffect(() => {
    if (data?.length) {
      dataHandler(data)
    } else {
      for (const menu of menuItems?.items ?? []) {
        if (menu.type === 'group') {
          const matchedParents = findParentElements(menu.children || [], pathname)
          dataHandler(matchedParents || [])
          if (matchedParents) break
        }
      }
    }
  }, [data, pathname])

  const dataHandler = (dataList: BreadcrumbItem[]) => {
    const active = dataList.at(-1)
    const linkItems = dataList.slice(0, -1)
    if (active && active.url !== homeBreadcrumb.url) {
      linkItems.unshift(homeBreadcrumb)
    }
    setActiveItem(active)
    setBreadcrumbItems(linkItems)
  }

  const findParentElements = (
    navItems: BreadcrumbItem[],
    targetUrl: string,
    parents: BreadcrumbItem[] = []
  ): BreadcrumbItem[] | null => {
    for (const item of navItems) {
      const newParents = [...parents, item]
      if (item.url && targetUrl.includes(item.url)) {
        return newParents
      }
      if (item.children) {
        const result = findParentElements(item.children, targetUrl, newParents)
        if (result) return result
      }
    }
    return null
  }

  return (
    <MuiBreadcrumbs aria-label='breadcrumb' separator={<IconChevronRight size={16} />}>
      {breadcrumbItems.length > 0 &&
        breadcrumbItems.map((item, index) => (
          <Typography
            key={index}
            {...(item.url
              ? {
                  component: Link,
                  to: item.url
                }
              : {})}
            variant='body2'
            sx={{
              'display': 'inline-flex',
              'alignItems': 'center',
              'p': 0.5,
              'color': 'grey.700',
              'textDecoration': 'none',
              ...(item.url && {
                'cursor': 'pointer',
                ':hover': { color: 'primary.main' }
              }),
              ':focus-visible': {
                outline: 'none',
                borderRadius: 0.25,
                ...generateFocusStyle(theme.palette.primary.main)
              }
            }}
          >
            {item.title}
          </Typography>
        ))}
      {activeItem && (
        <Typography variant='body2' sx={{ p: 0.5, display: 'inline-flex', alignItems: 'center' }}>
          {activeItem.title}
        </Typography>
      )}
    </MuiBreadcrumbs>
  )
}

import Divider from '@mui/material/Divider'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

import branding from '@/branding.json'

export const Copyright = () => {
  const currentYear = new Date().getFullYear()
  return (
    <Stack
      direction='row'
      sx={{ justifyContent: 'center', gap: { xs: 1, sm: 1.5 }, textAlign: 'center' }}
    >
      <Typography variant='caption' color='text.secondary'>
        {branding.brandName}
      </Typography>
      <Divider orientation='vertical' flexItem />
      <Typography variant='caption' color='text.secondary'>
        © {currentYear} {branding.companyName}
      </Typography>
    </Stack>
  )
}

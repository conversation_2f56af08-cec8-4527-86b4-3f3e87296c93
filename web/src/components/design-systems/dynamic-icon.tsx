import * as React from 'react'
import * as TablerIcons from '@tabler/icons-react'

type IconName = keyof typeof TablerIcons

interface DynamicIconProps {
  name: IconName
  size?: number
  color?: string
  stroke?: number
}

export const DynamicIcon = ({ name, size = 24, color = 'black', stroke = 2 }: DynamicIconProps) => {
  const IconComponent = TablerIcons[name] as React.ElementType | undefined

  if (!IconComponent) return null

  return <IconComponent size={size} color={color} stroke={stroke} />
}

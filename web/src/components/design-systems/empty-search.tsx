import * as React from 'react'
import Box from '@mui/material/Box'
import Stack, { type StackProps } from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

import { DumpingDoodle } from '@/components/images/dumping-doodle'

/***************************  HEADER - EMPTY SEARCH ***************************/

export const EmptySearch = React.forwardRef<HTMLDivElement, StackProps>((props, ref) => {
  const { sx, ...rest } = props

  return (
    <Stack
      ref={ref}
      {...rest}
      sx={{
        width: 1,
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        gap: 1.5,
        p: 1.5,
        ...sx
      }}
    >
      <Box sx={{ width: 230, height: 170 }}>
        <DumpingDoodle />
      </Box>
      <Stack sx={{ gap: 0.5, width: 220 }}>
        <Typography variant='h6' sx={{ fontWeight: 400 }}>
          No search Result
        </Typography>
        <Typography variant='caption' sx={{ color: 'text.secondary' }}>
          Didn’t found anything
        </Typography>
      </Stack>
    </Stack>
  )
})

EmptySearch.displayName = 'EmptySearch'

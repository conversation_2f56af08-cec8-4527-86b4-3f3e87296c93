import Alert from '@mui/material/Alert'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import FormHelperText from '@mui/material/FormHelperText'

import { useFieldContext, useFormContext } from '@/hooks/form-context'

export interface FormSubmitButtonProps extends Omit<React.ComponentProps<typeof Button>, 'type'> {}

export const FormSubmitButton = ({ children, ...props }: FormSubmitButtonProps) => {
  const form = useFormContext()
  return (
    <form.Subscribe selector={state => [state.isSubmitting]}>
      {([isSubmitting]) => (
        <Button
          type='submit'
          color='primary'
          variant='contained'
          disabled={isSubmitting}
          endIcon={isSubmitting && <CircularProgress color='secondary' size={16} />}
          sx={{ 'minWidth': 120, 'mt': { xs: 2, sm: 4 }, '& .MuiButton-endIcon': { ml: 1 } }}
          fullWidth
          {...props}
        >
          {children}
        </Button>
      )}
    </form.Subscribe>
  )
}

export interface FormSubmitErrorMessageProps
  extends Omit<React.ComponentProps<typeof Alert>, 'severity'> {}

export const FormSubmitErrorMessage = (props: FormSubmitErrorMessageProps) => {
  const form = useFormContext()

  return (
    <form.Subscribe selector={state => state.errorMap}>
      {errorMap =>
        errorMap.onSubmit && (
          <Alert severity='error' variant='filled' icon={false} {...props}>
            {errorMap.onSubmit.toString()}
          </Alert>
        )
      }
    </form.Subscribe>
  )
}

export const FieldErrorMessage = () => {
  const field = useFieldContext()

  return (
    <>
      {field.state.meta.isTouched && !field.state.meta.isValid && (
        <FormHelperText error>
          {field.state.meta.errors.map(error => {
            const errorType = typeof error
            if (errorType === 'string') {
              return error
            } else if (errorType === 'object' && error !== null && 'message' in error) {
              return error.message
            } else {
              return JSON.stringify(error)
            }
          })}
        </FormHelperText>
      )}
    </>
  )
}

import { forwardRef } from 'react'
import Card, { type CardProps } from '@mui/material/Card'
import type { SxProps, Theme } from '@mui/material/styles'

/***************************  MAIN CARD  ***************************/

export interface MainCardProps extends CardProps {
  sx?: SxProps<Theme>
}

export const MainCard = forwardRef<HTMLDivElement, MainCardProps>(function MainCard(
  { children, sx = {}, ...others },
  ref
) {
  const defaultSx: SxProps<Theme> = theme => ({
    p: { xs: 1.75, sm: 2.25, md: 3 },
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: 4,
    boxShadow: theme.customShadows.section
  })

  // 合并
  const mergedSx: SxProps<Theme> =
    typeof sx === 'function'
      ? theme => ({ ...defaultSx(theme), ...sx(theme) })
      : theme => ({ ...defaultSx(theme), ...(sx || {}) })

  return (
    <Card ref={ref} elevation={0} sx={mergedSx} {...others}>
      {children}
    </Card>
  )
})

import { isValidElement, type ReactElement } from 'react'
import Avatar, { type AvatarProps } from '@mui/material/Avatar'
import Badge from '@mui/material/Badge'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import { useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'

import { AvatarSize } from '@/theme/enum/avatar'

/***************************  NOTIFICATION - LIST  ***************************/

export interface NotificationItemProps {
  avatar: ReactElement | AvatarProps
  badgeAvatar?: AvatarProps
  title?: React.ReactNode
  subTitle?: React.ReactNode
  dateTime?: React.ReactNode
  isSeen?: boolean
}

export const NotificationItem = ({
  avatar,
  badgeAvatar,
  title,
  subTitle,
  dateTime,
  isSeen = false
}: NotificationItemProps) => {
  const theme = useTheme()
  const ellipsis = { textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }

  const avatarContent = isValidElement(avatar) ? avatar : <Avatar {...avatar} />

  return (
    <Stack
      direction='row'
      sx={{ width: 1, alignItems: 'center', justifyContent: 'space-between', gap: 1 }}
    >
      <Stack direction='row' sx={{ alignItems: 'center', gap: 1.25, flexShrink: 0 }}>
        {badgeAvatar ? (
          <Box>
            <Badge
              overlap='circular'
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
              badgeContent={
                <Avatar
                  sx={{ border: `1px solid ${theme.palette.common.white}` }}
                  {...badgeAvatar}
                  size={AvatarSize.BADGE}
                />
              }
              sx={{ '& .MuiBadge-badge': { bottom: '22%' } }}
            >
              {avatarContent}
            </Badge>
          </Box>
        ) : (
          avatarContent
        )}
      </Stack>

      <Stack sx={{ flexGrow: 1, minWidth: 0, maxWidth: 1, gap: 0.25 }}>
        <Typography
          variant={isSeen ? 'body2' : 'subtitle2'}
          {...(isSeen && { color: 'grey.700' })}
          noWrap
          sx={ellipsis}
        >
          {title}
        </Typography>
        {subTitle && (
          <Typography variant='caption' color='text.secondary' noWrap sx={ellipsis}>
            {subTitle}
          </Typography>
        )}
      </Stack>

      {dateTime && (
        <Typography
          variant='caption'
          sx={{ marginLeft: 'auto', flexShrink: 0 }}
          {...(isSeen && { color: 'grey.600' })}
        >
          {dateTime}
        </Typography>
      )}
    </Stack>
  )
}

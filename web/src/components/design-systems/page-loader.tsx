import Avatar from '@mui/material/Avatar'
import CircularProgress, { type CircularProgressProps } from '@mui/material/CircularProgress'
import Stack from '@mui/material/Stack'
import { keyframes } from '@mui/material/styles'

import { IconLogoSingle } from '../icons'

const rotateAnimation = keyframes`
  0% { transform: rotate(0deg) }
  100% { transform: rotate(-360deg) }
`

const dotAnimation = keyframes`
  0% { transform: rotate(-360deg) }
  100% { transform: rotate(0deg) }
`

export const PageLoader = () => {
  const commonProps: CircularProgressProps = {
    disableShrink: true,
    size: 100,
    variant: 'determinate',
    thickness: 4,
    color: 'primary'
  }

  return (
    <Stack sx={{ height: '100%', width: 1, alignItems: 'center', justifyContent: 'center' }}>
      <Stack
        direction='row'
        sx={{ position: 'relative', justifyContent: 'center', alignItems: 'center' }}
      >
        <Avatar
          sx={{
            'width': 65,
            'height': 65,
            'bgcolor': 'transparent',
            '& .MuiBox-root': { height: 30, width: 30 },
            '& svg': { width: 1, height: 1 }
          }}
        >
          <IconLogoSingle className='mb-1 h-full w-full' />
        </Avatar>
        <CircularProgress
          {...commonProps}
          value={100}
          sx={{
            'position': 'absolute',
            'zIndex': 1,
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
              strokeDasharray: '6 9.5 !important'
            },
            'animation': `${dotAnimation} 6s linear infinite`
          }}
        />
        <CircularProgress
          {...commonProps}
          value={60}
          sx={{
            'position': 'absolute',
            'zIndex': 1,
            '& .MuiCircularProgress-circle': { strokeLinecap: 'round' },
            'animation': `${rotateAnimation} 35s linear infinite`
          }}
        />
      </Stack>
    </Stack>
  )
}

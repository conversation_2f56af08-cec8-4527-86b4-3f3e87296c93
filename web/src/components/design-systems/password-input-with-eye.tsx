import * as React from 'react'
import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'
import InputAdornment from '@mui/material/InputAdornment'
import OutlinedInput from '@mui/material/OutlinedInput'

export const PasswordInputWithEye = React.forwardRef<
  HTMLInputElement,
  Omit<React.ComponentProps<typeof OutlinedInput>, 'type' | 'endAdornment'>
>((props, ref) => {
  const [type, setType] = React.useState<'password' | 'text'>('password')
  const [VisibilityOrVisibilityOff, setIcon] = React.useState(() => Visibility)

  const handleToggle = () => {
    if (type === 'password') {
      setIcon(VisibilityOff)
      setType('text')
    } else {
      setIcon(Visibility)
      setType('password')
    }
  }

  return (
    <OutlinedInput
      ref={ref}
      type={type}
      spellCheck='false'
      autoComplete='off'
      fullWidth
      endAdornment={
        <InputAdornment
          position='end'
          sx={{ cursor: 'pointer', WebkitTapHighlightColor: 'transparent' }}
          onClick={() => handleToggle()}
        >
          <VisibilityOrVisibilityOff />
        </InputAdornment>
      }
      {...props}
    />
  )
})
PasswordInputWithEye.displayName = 'PasswordInputWithEye'

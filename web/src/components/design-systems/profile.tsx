import { type ReactNode } from 'react'
import Avatar, { type AvatarProps } from '@mui/material/Avatar'
import Stack from '@mui/material/Stack'
import type { SxProps, Theme } from '@mui/material/styles'
import Typography, { type TypographyProps } from '@mui/material/Typography'

export interface ProfileProps {
  avatar?: AvatarProps
  title?: string
  caption?: ReactNode
  label?: ReactNode
  sx?: SxProps<Theme>
  titleProps?: TypographyProps
  captionProps?: TypographyProps
}

export const Profile = ({
  avatar,
  title,
  caption,
  label,
  sx,
  titleProps,
  captionProps
}: ProfileProps) => {
  return (
    <Stack
      direction='row'
      sx={{
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 0.75,
        width: 'fit-content',
        ...sx
      }}
    >
      {avatar && <Avatar {...avatar} alt='profile' />}
      <Stack sx={{ gap: 0.25 }}>
        <Stack direction='row' sx={{ alignItems: 'center', gap: 0.5 }}>
          <Typography
            variant='subtitle2'
            {...titleProps}
            sx={{ whiteSpace: 'nowrap', ...titleProps?.sx }}
          >
            {title}
          </Typography>
          {label}
        </Stack>
        {caption && (
          <Typography
            variant='caption'
            {...captionProps}
            sx={{ color: 'grey.700', ...captionProps?.sx }}
          >
            {caption}
          </Typography>
        )}
      </Stack>
    </Stack>
  )
}

import * as React from 'react'
import Alert from '@mui/material/Alert'
import Button from '@mui/material/Button'
import Fade from '@mui/material/Fade'
import Grow from '@mui/material/Grow'
import IconButton from '@mui/material/IconButton'
import Slide from '@mui/material/Slide'
import MuiSnackbar, { type SnackbarCloseReason } from '@mui/material/Snackbar'
import { alpha } from '@mui/material/styles'
import Zoom from '@mui/material/Zoom'
import { IconX } from '@tabler/icons-react'

import { useSnackbarStore } from '@/stores/snackbar'

// -------------------- 动画映射 --------------------

// 提供每种动画组件的显式类型封装
const TransitionSlideLeft = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='left' />
)
const TransitionSlideUp = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='up' />
)
const TransitionSlideRight = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='right' />
)
const TransitionSlideDown = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='down' />
)
const GrowTransition = (props: React.ComponentProps<typeof Grow>) => <Grow {...props} />
const ZoomTransition = (props: React.ComponentProps<typeof Zoom>) => <Zoom {...props} />

// 使用 Record<string, React.ComponentType<any>> 替代 any 类型，明确类型约束
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const animationMap: Record<string, React.ComponentType<any>> = {
  SlideLeft: TransitionSlideLeft,
  SlideUp: TransitionSlideUp,
  SlideRight: TransitionSlideRight,
  SlideDown: TransitionSlideDown,
  Grow: GrowTransition,
  Zoom: ZoomTransition,
  Fade
}

const closeIconSx = {
  'p': 0.75,
  'mt': -0.25,
  'width': 30,
  'height': 30,
  '& svg': {
    transition: 'transform 0.2s ease-in-out'
  }
}

const closeIconScale = { transform: 'scale(1.2)' }

// -------------------- 主组件定义 --------------------

export const Snackbar = () => {
  const snackbar = useSnackbarStore() // ✅ 统一对象访问，提升可维护性

  const handleClose = (_: unknown, reason?: SnackbarCloseReason) => {
    if (reason === 'clickaway') return
    snackbar.closeSnackbar()
  }

  return (
    <>
      {snackbar.variant === 'default' && (
        <MuiSnackbar
          anchorOrigin={snackbar.anchorOrigin}
          open={snackbar.open}
          autoHideDuration={1500}
          onClose={handleClose}
          message={snackbar.message}
          slots={{ transition: animationMap[snackbar.transition] }}
          action={
            <>
              <Button
                onClick={handleClose}
                color='secondary'
                sx={theme => ({
                  ...theme.typography.caption,
                  'height': 24,
                  'color': 'background.paper',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.error.main, 0.6)
                  }
                })}
              >
                UNDO
              </Button>
              <IconButton
                aria-label='close'
                color='secondary'
                onClick={handleClose}
                sx={{
                  ...closeIconSx,
                  'color': 'inherit',
                  '&:hover': {
                    'bgcolor': 'transparent',
                    '& svg': closeIconScale
                  }
                }}
              >
                <IconX />
              </IconButton>
            </>
          }
          sx={{ '& .MuiPaper-root': { bgcolor: 'secondary.main' } }}
        />
      )}

      {snackbar.variant === 'alert' && (
        <MuiSnackbar
          anchorOrigin={snackbar.anchorOrigin}
          open={snackbar.open}
          autoHideDuration={1500}
          onClose={handleClose}
          slots={{ transition: animationMap[snackbar.transition] }}
        >
          <Alert
            variant={snackbar.alert.variant}
            color={snackbar.alert.color}
            action={
              <>
                {snackbar.actionButton !== false && (
                  <>
                    <Button
                      color={snackbar.alert.color}
                      size='small'
                      onClick={handleClose}
                      sx={{ mt: 0, height: 25 }}
                    >
                      UNDO
                    </Button>
                    <IconButton
                      aria-label='close'
                      color={snackbar.alert.color}
                      onClick={handleClose}
                      sx={{
                        ...closeIconSx,
                        '&:hover': {
                          'bgcolor': 'transparent',
                          '& svg': closeIconScale
                        }
                      }}
                    >
                      <IconX />
                    </IconButton>
                  </>
                )}
                {snackbar.actionButton === false && snackbar.close && (
                  <IconButton
                    aria-label='close'
                    color={snackbar.alert.color}
                    onClick={handleClose}
                    sx={{
                      ...closeIconSx,
                      'color': 'inherit',
                      '&:hover': {
                        '& svg': closeIconScale
                      }
                    }}
                  >
                    <IconX />
                  </IconButton>
                )}
              </>
            }
            sx={{
              ...snackbar.alert.sx,
              ...(snackbar.alert.variant === 'outlined' && {
                bgcolor: 'background.default'
              })
            }}
          >
            {snackbar.message}
          </Alert>
        </MuiSnackbar>
      )}
    </>
  )
}

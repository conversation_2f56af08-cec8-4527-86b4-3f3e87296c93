import * as React from 'react'

import { cn } from '@/lib/utils'
import feishuImg from '/images/feishu.png'

export const IconFeishu = React.forwardRef<
  HTMLImageElement,
  Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src' | 'alt'>
>(({ className, ...props }, ref) => {
  return (
    <img
      ref={ref}
      src={feishuImg}
      alt='飞书'
      className={cn('w-5', className)}
      width={30}
      height={30}
      {...props}
    />
  )
})
IconFeishu.displayName = 'IconFeishu'

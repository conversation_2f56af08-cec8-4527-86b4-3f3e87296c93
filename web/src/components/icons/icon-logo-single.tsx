import * as React from 'react'

import { cn } from '@/lib/utils'
import logoImg from '/images/logo-single.png'

export const IconLogoSingle = React.forwardRef<
  HTMLImageElement,
  Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src' | 'alt'>
>(({ className, ...props }, ref) => (
  <img
    ref={ref}
    src={logoImg}
    alt='极安科技'
    className={cn('w-10', className)}
    width={256}
    height={256}
    {...props}
  />
))
IconLogoSingle.displayName = 'LogoSingle'

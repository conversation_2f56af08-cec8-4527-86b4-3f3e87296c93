import React from 'react'

import { cn } from '@/lib/utils'
import logoImg from '/images/logo.png'

export const IconLogo = React.forwardRef<
  HTMLImageElement,
  Omit<React.ImgHTMLAttributes<HTMLImageElement>, 'src' | 'alt'>
>(({ className, ...props }, ref) => {
  return (
    <img
      ref={ref}
      src={logoImg}
      alt='极安科技'
      className={cn('w-32', className)}
      width={512}
      height={256}
      {...props}
    />
  )
})
IconLogo.displayName = 'Logo'

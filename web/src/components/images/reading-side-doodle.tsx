import { useTheme } from '@mui/material/styles'

/***************************  ILLUSTARION -  READING SIDE DOODLE  ***************************/

export const ReadingSideDoodle = () => {
  const theme = useTheme()

  return (
    <svg viewBox='0 0 1024 768' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M738.321 565.16C733.804 570.384 727.134 574.261 722.016 579.037C716.642 584.054 710.809 588.438 705.456 593.394C681.842 564.533 649.494 546.836 621.471 522.887C608.186 511.532 598.398 496.131 591.235 480.325C587.563 472.222 584.85 463.882 582.727 455.25C581.771 451.362 580.836 447.487 580.211 443.546C579.454 438.773 579.258 438.366 576.038 434.641C574.652 433.038 571.967 428.354 569.252 428.87C566.967 429.305 562.788 436.26 561.344 437.978C554.4 446.241 547.668 454.755 540.357 462.698C529.172 474.852 518.989 486.812 508.604 499.694C487.36 526.049 464.444 550.858 441.32 575.657C439.605 577.496 435.606 582.889 433.142 583.614C430.146 584.495 425.277 580.82 422.26 578.766C414.977 573.808 409.237 567.671 403.011 561.526C390.862 549.534 382.635 532.701 377.101 516.685C373.788 507.098 371.634 497.16 368.215 487.606C366.79 483.624 363.56 478.73 365.2 475.262C365.688 474.228 368.796 471.481 369.892 471.271C371.188 471.022 373.349 472.799 373.799 472.676C378.099 471.499 375.713 466.998 378.271 464.576C379.721 463.202 387.096 463.403 389.298 462.648C397.349 459.886 404.572 454.618 412.183 451.473C444.046 438.309 476.297 425.504 503.804 404.007C510.938 398.433 515.678 394.244 524.19 390.641C532.792 387 541.439 383.752 550.216 380.575C557.96 377.772 565.746 375.144 573.746 373.135C582.396 370.962 594.667 368.207 603.31 371.006C620.283 376.501 625.764 390.486 632.523 405.239C641.668 425.199 652.725 444.087 664.712 462.459C687.772 497.807 715.818 529.526 738.321 565.16Z'
        fill={theme.palette.primary.lighter}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M227.16 252.662C226.226 252.188 218.934 255.156 217.71 255.61C212.821 257.424 208.216 260.438 203.61 262.89C207.14 267.186 210.671 271.483 214.2 275.78C217.66 267.293 222.107 260.29 227.16 252.662ZM229.567 305.467C228.366 305.602 238.506 329.969 239.69 332.705C243.999 342.656 248.523 352.51 252.889 362.436C257.119 372.055 262.485 380.904 267.891 389.89C272.515 397.575 277.271 409.501 284.246 415.193C279.842 395.655 280.422 373.19 267.79 356.785C254.696 339.778 241.75 323.171 229.567 305.467ZM738.321 566.027C718.754 535.042 694.26 507.631 673.856 477.199C662.431 460.159 651.311 442.901 641.694 424.763C634.699 411.569 630.351 392.744 619.547 382.657C614.786 378.213 609.699 373.863 603.31 371.872C594.567 369.148 582.529 371.848 573.746 374.002C557.379 378.015 541.39 384.247 525.887 390.792C510.162 397.43 496.746 409.354 482.538 418.934C467.297 429.212 450.799 436.699 433.723 443.253C418.855 448.96 405.628 457.153 390.839 462.897C387.892 464.042 381.878 463.874 379.406 465.367C376.742 466.974 377.791 471.72 374.557 473.471C373.098 474.261 372.612 471.397 371.492 471.444C370.122 471.501 368.873 473.109 367.553 474.127C362.178 478.274 365.471 481.039 367.599 486.745C374.536 505.337 378.264 524.253 388.004 541.805C396.012 556.232 407.473 569.16 420.967 578.711C423.02 580.165 429.711 585.797 432.12 585.422C434.289 585.086 438.702 579.32 440.2 577.754C446.995 570.648 453.206 563.004 459.997 555.887C470.491 544.888 480.593 533.594 490.777 522.445C502.599 509.502 513.062 494.837 523.71 481.008C528.608 474.646 534.913 469.464 540.357 463.565C547.678 455.633 554.4 447.106 561.345 438.845C563.445 436.345 566.965 429.95 570.23 430.025C572.518 430.078 576.552 435.465 577.749 437.153C582.197 443.428 582.177 454.76 584.389 462.593C589.787 481.693 599.52 500.348 612.869 515.087C626.143 529.744 643.147 540.167 658.847 551.948C675.855 564.712 691.968 577.774 705.456 594.261C710.809 589.305 716.642 584.92 722.016 579.904C727.135 575.127 733.805 571.251 738.321 566.027ZM486.452 653.687C490.256 653.726 496.698 656.066 500.101 654.657C490.007 643.488 480.737 633.905 467.966 625.72C454.713 617.226 441.504 608.808 428.632 599.748C402.691 581.49 383.611 559.837 372.775 529.431C369.985 521.601 368.116 513.389 366.071 505.34C364.971 501.005 362.781 480.725 359.062 478.974C356.859 477.936 340.279 489.589 337.652 491.041C331.51 494.438 325.345 497.072 318.995 499.73C305.346 505.445 291.275 509.804 276.801 513.443C259.448 517.806 247.628 510.587 236.878 496.948C224.682 481.474 214.547 464.015 203.396 447.765C198.325 440.374 194.124 432.995 190.379 424.843C187.928 419.509 178.207 405.118 181.566 399.435C191.149 406.731 194.597 421.238 201.125 431.007C209.261 443.182 218.43 454.661 226.608 466.825C233.485 477.053 240.501 489.448 250.661 496.801C259.598 503.268 268.088 501.28 277.706 498.373C289.363 494.851 301.446 493.115 312.87 488.746C325.233 484.019 336.93 477.313 348.992 471.845C354.615 469.295 361.348 467.846 366.647 464.667C371.214 461.929 370.533 460.483 370.868 454.652C371.26 447.849 371.711 441.041 372.012 434.232C372.214 429.657 369.74 423.454 371.419 420.039C357.725 422.413 344.341 425.593 330.743 428.406C323.298 429.946 316.119 431.85 308.598 433.06C305.731 433.521 301.812 433.508 299.245 434.757C297.197 434.169 296.153 434.543 296.113 435.878C296.015 436.603 295.86 437.318 295.647 438.021C291.448 439.931 289.006 435.834 285.364 431.904C280.87 427.054 277.505 421.592 274.208 415.893C266.836 403.151 258.339 391.167 251.517 378.066C244.564 364.711 238.917 350.412 233.022 336.556C227.614 323.844 224.151 310.096 218.175 297.685C215.508 292.145 212.128 287.564 208.181 282.884C204.924 279.022 199.913 270.691 195.049 268.917C193.763 268.448 190.395 269.236 188.934 269.132C186.022 268.924 183.427 267.855 180.447 268.089C173.995 268.594 166.961 272.674 161.805 276.261C165.137 279.231 173.472 282.841 167.485 288.191C161.675 293.385 158.466 284.068 156.249 279.986C145.809 288.559 147.318 300.38 146.112 312.493C145.482 318.817 143.825 325.045 142.756 331.274C141.986 335.757 142.526 340.394 142.306 344.971C141.745 356.602 140.576 367.735 140.631 379.203C140.693 391.461 141.528 403.299 142.509 415.468C144.16 435.956 147.943 456.451 151.187 476.776C153.012 488.201 155.51 499.301 157.935 510.541C160.687 523.298 165.778 535.381 168.633 548.105C171.542 544.407 173.73 544.389 178.435 544.879C182.054 545.256 184.485 544.395 184.273 549.418C184.136 552.676 181.262 555.164 178.082 555.733C173.935 556.474 172.526 554.405 170.389 551.264C169.889 557.212 173.889 563.431 175.471 569.24C176.968 574.745 177.96 580.141 180.476 585.41C182.931 590.55 185.033 595.62 187.517 600.711C188.661 603.053 189.731 605.432 190.888 607.768C191.473 608.951 192.162 610.581 193.035 611.578C195.083 613.915 194.757 612.783 197.154 614.114C202.376 617.015 200.415 616.292 200.537 622.282C200.656 628.191 203.686 632.532 208.545 635.853C218.854 642.901 229.022 644.861 240.742 648.422C251.662 651.74 263.164 654.587 274.727 654.522C288.889 654.443 302.863 652.586 317.025 652.398C332.649 652.191 348.423 652.276 364.015 652.816C370.438 653.038 376.403 653.324 382.604 654.964C389.805 656.87 396.771 656.114 404.193 655.825C420.027 655.209 436.407 653.984 452.034 653.874C463.286 653.794 475.158 653.009 486.452 653.687ZM897.04 659.825C899.855 661.312 906.165 660.702 908.995 660.289C913.98 659.562 914.514 657.586 917.677 653.141C923.121 645.486 927.541 638.155 930.744 629.288C936.874 612.317 939.377 594.955 942.068 577.202C945.059 557.476 939.597 537.029 937.643 517.437C937.194 512.942 936.58 508.488 935.87 504.027C935.445 501.36 935.334 492.287 933.579 489.978C930.207 485.542 914.527 489.12 908.362 489.522C891.154 490.646 873.833 490.909 856.633 491.104C847.462 491.208 838.24 491.592 829.16 491.663C824.99 491.696 820.913 491.489 816.728 491.083C815.328 490.948 813.961 490.626 812.551 490.548C807.723 490.281 808.086 489.581 806.838 493.856C804.652 501.346 806.334 510.875 805.76 518.649C804.992 529.037 804.577 539.572 804.767 549.982C804.943 559.602 804.615 569.2 804.974 578.841C805.124 582.834 805.56 586.089 806.613 589.902C807.804 594.213 809.114 604.285 812.08 607.268C812.417 607.608 814.938 606.816 815.607 607.662C816.638 608.965 816.449 611.722 817.157 612.564C819.448 615.29 824.183 615.506 827.618 616.731C837.043 620.092 845.689 623.741 854.572 628.273C862.509 632.322 875.306 635.768 880.078 643.756C882.266 647.418 882.561 652.247 884.573 656.025C886.864 660.329 882.28 658.079 887.893 659.825C890.421 660.611 894.449 659.825 897.04 659.825ZM60.285 611.147C59.571 621.086 53.972 630.553 52.023 640.325C51.111 644.901 49.564 649.399 48.492 653.948C47.464 658.312 43.75 663.48 44.126 667.798C57.834 662.914 71.719 661.686 86.046 660.369C99.625 659.12 113.97 658.623 127.435 658.617C141.156 658.61 155.827 657.27 169.955 657.062C184.73 656.845 199.728 656.462 214.289 654.93C206.675 649.291 197.148 643.995 191.526 636.149C185.414 627.62 182.24 616.934 176.862 607.934C166.07 589.873 159.209 567.462 153.552 547.171C142.131 506.209 129.368 466.003 126.292 423.515C120.438 426.513 115.791 438.144 112.86 443.936C109.119 451.331 107.16 459.347 104.728 467.207C99.72 483.396 96.254 500.182 90.122 516.217C78.402 546.869 70.869 579.897 60.285 611.147ZM804.397 372.41C806.932 390.73 812.248 409.287 812.452 427.828C812.497 431.907 811.806 436.437 812.656 440.459C813.564 444.753 816.517 448.549 818.222 452.63C820.928 459.108 827.781 472.386 821.172 477.875C821.038 472.666 816.271 466.051 813.87 461.496C810.251 454.633 805.496 448.358 801.573 441.665C793.361 427.656 784.159 413.947 773.576 401.597C763.268 389.567 751.388 378.896 740.457 367.441C737.173 363.999 710.827 330.872 709.78 331.335C717.952 362.849 731.844 390.269 750.855 416.655C760.176 429.59 771.363 438.102 782.71 449.06C792.159 458.186 809.968 467.944 806.701 482.897C821.472 481.477 836.146 479.208 851.051 479.208C848.6 460.343 846.723 441.777 842.347 423.222C837.379 402.153 834.014 380.454 829.81 359.198C825.52 337.498 818.243 316.49 813.716 294.831C809.381 274.091 805.657 253.388 802.479 232.417C799.853 244.037 800.822 256.8 799.89 268.656C798.938 280.769 799.03 292.659 799.347 304.843C799.934 327.474 802.346 349.832 804.397 372.41ZM868.403 264.512C868.486 254.825 870.363 245.269 870.942 235.605C871.12 232.64 874.073 212.411 871.319 211.355C869.054 216.293 868.526 222.612 866.469 227.793C864.414 232.969 861.665 237.9 859.693 243.112C855.421 254.403 853.947 267.179 853.487 279.183C852.446 306.335 851.908 333.426 853.733 360.578C854.515 372.216 855.115 383.874 854.901 395.535C854.666 408.366 852.701 419.503 855.087 432.229C857.449 444.831 858.933 457.381 859.72 470.167C860.272 479.141 860.494 478.506 869.056 477.875C875.237 477.419 881.479 477.906 887.669 477.623C884.775 442.647 884.302 407.599 879.679 372.757C877.32 354.982 877.257 336.95 874.327 319.241C872.871 310.444 870.428 301.824 869.024 293.017C867.517 283.561 868.259 274.041 868.403 264.512ZM242.295 242.501C258.778 243.929 276.311 248.857 293.206 245.603C290.421 244.475 283.133 242.919 286.413 238.467C289.748 233.94 295.368 239.682 298.694 239.089C300.745 238.723 305.277 230.15 306.541 228.375C309.41 224.341 309.756 223.638 314.915 222.604C319.913 221.604 321.046 222.687 321.679 217.718C322.406 212.004 320.429 205.58 320.625 199.737C321.046 187.158 325.104 176.127 328.881 164.292C312.834 167.783 297.143 171.923 280.735 174.173C273.721 175.135 262.809 177.354 256.437 172.586C254.78 171.346 255.045 169.672 253.689 168.235C251.415 165.824 247.197 163.33 243.972 162.643C224.125 158.418 222.189 186.711 232.135 197.298C234.626 199.951 238.092 201.261 240.643 203.692C244.048 206.934 244.327 210.349 244.824 215.298C245.756 224.578 244.49 233.894 240.531 242.383L242.295 242.501ZM483.14 376.071C488.999 377.183 495.114 376.929 500.988 377.878C503.075 378.215 509.332 379.391 511.167 378.171C513.969 376.308 514.965 369.475 516.29 366.293C518.62 360.696 521.917 355.814 524.139 350.115C533.219 326.837 541.686 303.466 552.668 280.983C548.459 279.889 545.227 279.846 540.985 279.281C536.566 278.692 532.56 276.079 528.344 274.956C524.337 273.889 520.139 274.161 516.169 272.92C513.982 272.237 512.105 270.718 509.949 270.187C506.512 269.342 502.501 268.848 500.457 271.104C498.362 273.418 497.627 279.763 496.487 282.752C493.451 290.715 489.924 298.4 487.381 306.546C485.086 313.893 482.906 321.162 480.886 328.576C478.752 336.403 474.546 342.854 471.984 350.331C470.035 356.022 468.196 361.84 467.905 367.908C467.622 373.829 466.76 374.206 472.681 375.152C475.67 375.629 480.328 377.405 483.14 376.071ZM868.63 653.187C870.156 653.898 871.595 653.804 872.945 652.903C859.715 647.315 846.846 639.565 833.044 635.731C820.097 632.136 807.513 627.84 795.04 623.412C784.905 619.814 775.793 617.013 766.63 610.681C761.068 606.837 756.127 602.515 752.305 596.898C748.05 590.644 747.103 581.744 742.495 576.112C736.275 582.943 728.968 588.116 721.609 593.544C719.063 595.422 712.956 599.516 712.085 602.422C710.986 606.089 713.93 609.597 715.386 613.308C719.115 622.812 720.937 632.311 722.96 642.192C723.771 646.152 725.522 652.105 728.775 653.764C731.737 655.273 740.315 654.176 744.001 653.896C763.231 652.439 782.12 650.947 801.466 652.274C823.358 653.775 846.795 654.812 868.63 653.187ZM907.039 477.075C910.534 477.087 913.948 476.65 917.43 476.65C915.42 467.265 920.119 455.121 921.785 445.753C923.649 435.274 925.068 424.438 926.311 413.875C928.931 391.626 930.118 369.647 930.152 347.235C930.184 325.548 934.132 305.73 939.662 284.882C945.6 262.492 955.324 242.357 963.364 220.764C954.545 225.426 949.663 236.335 945.029 244.675C939.171 255.22 935.269 266.896 932.877 278.734C928.262 301.571 923.2 324.477 917.875 347.276C912.33 371.015 909.07 394.972 902.718 418.517C899.62 429.999 895.408 444.125 895.174 455.966C895.077 460.864 893.601 471.122 895.821 475.376C897.897 479.356 902.487 477.481 907.039 477.075ZM379.488 447.621C379.304 450.059 379.13 452.378 378.697 454.786C383.538 454.74 389.669 449.717 393.821 447.462C398.879 444.715 403.943 441.685 408.89 438.924C418.44 433.592 429.282 430.079 439.479 425.699C451.193 420.667 461.96 414.587 473.162 408.529C477.075 406.413 505.197 394.223 504.725 390.893C498.551 390.687 492.377 390.482 486.202 390.277C480.909 390.101 472.711 391.297 467.642 389.661C464.866 388.764 461.749 385.463 460.701 382.878C459.664 380.32 461.995 368.555 460.64 367.805C453.73 374.86 449.385 384.237 443.207 391.848C436.38 400.26 427.558 405.172 417.835 409.642C407.098 414.579 395.985 418.402 385.012 422.633C380.384 424.417 380.476 425.438 380.073 430.512C379.622 436.183 379.671 441.935 379.488 447.621ZM937.435 660.981C949.074 662.23 961.002 661.518 972.732 662.492C977 662.846 982.917 663.374 978.623 669.102C975.919 672.71 967.257 671.553 963.328 671.803C952.779 672.474 941.61 672.113 931.037 671.957C928.811 671.925 926.351 671.263 924.101 671.412C921.504 671.583 918.394 673.451 916.027 673.437C914.568 673.428 914.045 671.889 912.787 671.748C910.046 671.44 907.306 671.215 904.542 671.216C862.752 671.225 821.536 670.385 779.85 670.34C759.917 670.319 739.664 669.975 719.739 669.039C715.555 668.842 691.727 669.698 692.387 662.164C692.726 658.284 700.503 657.737 703.341 657.325C705.444 657.02 707.626 657.068 709.733 656.955C715.331 656.655 717.783 658.002 716.602 651.877C712.123 628.645 704.097 609.445 686.949 592.741C672.927 579.082 656.043 566.692 640.161 555.286C620.877 541.436 605.233 526.87 593.784 505.677C587.098 493.301 581.501 480.648 578.066 466.964C576.83 462.041 576.801 456.442 575.286 451.696C574.121 448.042 571.465 445.485 569.901 441.7C550.715 473.746 525.286 500.557 501.97 529.451C490.829 543.255 478.12 556.366 464.809 568.375C460.859 571.939 457.173 575.66 453.486 579.495C450.402 582.705 445.506 585.558 442.902 589.119C440.404 592.535 441.019 592.372 444.821 595.085C449.847 598.67 455.386 601.649 460.491 605.145C468.651 610.734 477.558 615.456 484.854 622.182C492.745 629.456 499.639 637.882 506.536 646.084C511.133 651.551 517.165 658.566 518.321 665.478C518.704 667.765 520.158 673.421 518.239 675.096C516.599 676.527 516.764 675.984 514.728 675.111C512.06 673.967 511.275 671.342 508.016 670.091C499.677 666.891 489.586 667.99 480.909 667.951C472.229 667.912 463.477 668.887 454.801 668.851C434.989 668.77 415.599 670.374 395.793 671.235C374.757 672.149 353.463 669.955 332.497 670.266C316.963 670.496 301.015 669.335 285.052 670.198C276.841 670.641 268.777 669.614 260.771 669.499C250.289 669.348 239.524 670.774 228.952 670.977C211.6 671.312 194.46 671.31 177.002 671.727C139.45 672.626 102.101 673.554 64.527 674.149C59.515 674.228 54.028 674.058 49.075 674.622C43.818 675.222 46.532 676.339 42.925 678.232C32.827 683.529 35.159 663.893 35.401 660.031C36.699 639.335 41.953 619.037 48.267 599.389C54.257 580.754 59.12 561.25 64.193 542.212C69.746 521.375 76.981 501.391 83.551 480.759C90.283 459.617 95.764 437.651 107.991 418.91C110.776 414.642 113.654 410.049 118.134 407.358C123.156 404.341 123.372 406.103 123.368 400.582C123.365 395.382 123.685 388.435 122.458 383.473C121.349 378.99 121.52 381.648 119.023 378.428C116.991 375.809 114.865 374.019 113.407 370.839C104.451 379.92 83.95 375.128 75.297 368.589C64.706 360.585 56.639 347.082 53.683 334.222C51.309 323.895 54.222 304.21 65.652 299.244C71.224 296.823 91.69 294.993 92.509 287.103C92.931 283.03 85.728 277.303 84.066 273.801C80.986 267.313 78.028 258.683 77.172 251.571C75.5 237.689 81.802 222.166 91.841 212.789C110.257 195.587 142.66 186.759 166.349 197.673C161.343 178.55 158.321 162.053 160.842 142.161C163.014 125.024 173.125 115.436 187.038 106.9C210.78 92.3321 243.066 86.5821 270.213 90.8181C276.382 91.7811 282.302 93.8461 288.014 95.4891C296.312 97.8761 306.246 97.0811 314.081 100.959C319.762 103.77 324.076 109.37 329.065 113.109C334.89 117.474 339.73 123.989 343.777 130.014C352.432 142.898 349.05 166.81 341.806 179.743C340.253 182.517 338.334 184.852 335.764 186.745C333.088 188.715 329.009 189.328 327.63 191.883C324.098 198.428 327.505 212.597 327.976 219.671C328.525 227.885 324.372 234.055 315.058 234.71C309.67 235.09 309.498 231.964 306.918 238.396C305.246 242.565 303.727 247.107 301.314 250.929C296.339 258.806 287.227 258.037 278.927 257.824C269.319 257.576 259.855 256.058 250.242 255.1C252.781 263.235 259.731 269.549 264.071 276.796C269.939 286.598 273.471 294.723 274.425 306.143C275.157 314.917 273.577 321.846 267.573 328.376C265.088 331.079 262.885 331.212 263.502 334.33C263.878 336.232 267.167 339.37 268.331 340.888C271.41 344.904 274.985 348.59 277.695 352.879C288.123 369.381 291.203 394.537 291.229 413.747C291.23 414.628 291.7 416.501 291.311 417.412C291.023 418.085 288.835 420.216 288.803 420.399C288.375 422.869 290.783 427.103 294.562 427.522C298.531 427.961 305.8 424.649 309.492 423.561C315.4 421.819 321.422 420.533 327.317 418.744C336.838 415.855 346.315 413.551 355.994 411.287C360.368 410.265 364.766 409.413 369.154 408.457C374.152 407.369 376.474 405.266 379.054 410.351C381.267 414.715 379.891 415.506 385.252 413.307C390.575 411.122 395.776 408.414 400.973 405.948C410.139 401.6 421.055 397.724 428.962 391.305C442.151 380.596 447.035 363.542 459.658 352.179C463.173 349.015 465.27 348.959 467.492 344.576C469.871 339.885 471.098 333.869 472.348 328.823C474.539 319.987 476.098 311.12 478.085 302.292C480.56 291.294 485.909 281.334 489.812 270.844C491.333 266.757 492.121 261.562 494.574 257.889C495.224 256.915 496.466 256.366 496.973 255.56C498.718 252.793 498.7 252.033 499.152 248.704C501.267 233.149 510.552 241.332 520.698 244.899C524.973 246.402 545.272 256.149 537.521 262.631C546.761 265.312 561.56 263.855 561.436 277.097C561.372 283.882 557.314 291.14 555.136 297.491C553.201 303.13 551.331 308.791 549.366 314.421C545.061 326.754 539.838 338.718 535.637 351.089C533.722 356.727 532.142 362.497 529.476 367.818C527.125 372.512 521.432 377.686 520.494 382.819C545.117 372.314 567.896 357.126 595.457 358.094C607.525 358.517 618.477 363.59 625.142 374.201C633.498 387.501 638.996 402.392 647.163 415.815C676.874 464.644 712.31 509.004 744.926 555.812C747.431 559.409 750.653 562.81 751.522 567.255C752.089 570.155 750.989 573.056 751.669 575.842C753.08 581.632 760.209 588.861 765.043 592.188C774.309 598.566 789.372 607.562 800.935 606.766C793.678 589.326 793.775 570.217 794.112 551.614C794.433 533.943 796.343 516.414 798.61 498.925C799.123 494.968 799.57 491.3 800.707 487.465C802.012 483.065 803.804 483.482 799.895 479.527C793.879 473.437 786.723 468.426 780.658 462.26C767.541 448.926 754.097 438.475 743.773 422.552C733.955 407.407 723.597 391.824 716.329 375.239C708.847 358.166 705.856 339.933 702.142 321.852C700.635 314.515 695.139 306.324 696.243 298.662C697.639 288.979 702.129 296.03 703.957 301.241C709.535 317.148 722.596 331.385 733.414 344.178C745.102 357.998 759.397 369.561 771.597 382.979C784.882 397.593 793.982 414.388 805.796 429.988C805.796 410.628 799.186 390.914 796.482 371.762C794.103 354.905 793.356 337.995 791.852 321.065C788.437 282.607 791.388 243.016 796.181 204.833C798.459 186.699 797.167 168.992 797.236 150.796C797.258 145.005 794.861 131.658 797.916 126.519C803.767 116.676 807.18 138.199 807.476 141.408C809.245 160.524 806.072 179.427 807.693 198.461C809.387 218.338 813.063 237.657 816.191 257.304C819.58 278.577 825.097 299.37 830.379 320.235C835.925 342.142 840.589 364.506 846.755 386.238C848.418 379.407 846.085 371.015 845.624 364.058C844.966 354.102 843.681 344.144 843.57 334.162C843.392 318.253 843.268 302.049 844.168 286.214C845.055 270.606 844.787 254.251 849.574 239.214C851.846 232.077 855.178 225.114 858.438 218.391C862.46 210.101 863.882 202.063 866.109 193.246C868.017 185.695 870.809 178.343 872.08 170.641C872.87 165.851 875.075 151.711 880.559 162.424C883.743 168.642 882.367 177.86 882.456 184.54C882.57 193.104 881.259 201.431 881.058 209.959C880.667 226.691 880.386 243.403 879.138 260.107C877.829 277.632 878.972 293.338 882.306 310.606C885.955 329.508 887.023 348.451 888.914 367.584C889.889 377.463 891.533 387.287 892.327 397.18C892.747 402.419 890.593 417.442 894.347 421.173C894.71 412.099 897.31 403.017 899.281 394.175C901.616 383.7 902.559 372.688 904.28 362.063C907.067 344.849 912.475 328.41 916.053 311.389C923.428 276.302 929.556 231.207 960.464 208.241C964.508 205.236 968.952 203.018 973.324 200.545C975.799 199.145 979.476 195.523 982.308 194.891C985.399 194.201 986.688 194.737 987.941 198.323C989.688 203.322 986.493 205.657 983.51 209.288C973.187 221.852 968.777 237.958 962.403 252.693C947.807 286.434 941.349 322.022 941.275 358.494C941.236 377.587 939.919 396.002 937.883 414.979C935.692 435.402 932.807 457.002 926.602 476.649C932.825 478.044 939.577 475.964 941.266 483.198C941.681 484.975 940.931 487.076 941.39 489.389C941.836 491.637 942.909 493.873 943.544 496.084C944.856 500.657 945.55 505.169 946.188 509.865C948.655 528.013 952.891 545.274 953.342 563.675C953.757 580.684 950.427 597.917 947.359 614.599C944.268 631.401 935.403 645.724 928.424 660.919C931.428 660.919 934.431 660.95 937.435 660.981Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M542.434 622.358C539.124 623.294 523.145 625.06 523.148 630.074C523.15 633.081 539.108 633.022 541.932 633.349C542.1 629.686 542.267 626.022 542.434 622.358ZM577.705 656.957C586.913 658.708 599.412 656.246 605.299 648.128C608.05 644.335 609.354 638.566 610.25 634.039C611.106 629.725 613.745 618.96 611.949 614.916C609.744 609.95 588.587 615.14 582.426 615.71C571.677 616.702 560.562 616.819 549.806 616.118C549.806 627.224 549.238 638.048 555.771 647.607C560.484 654.504 569.333 656.132 577.705 656.957ZM575.854 670.675C565.849 667.013 555.345 669.393 548.83 658.652C547.177 655.927 545.785 652.997 544.785 649.97C544.296 648.644 543.916 647.286 543.646 645.895C544.155 642.901 543.058 642.057 540.356 643.362C535.053 642.331 529.238 645.971 523.652 644.898C510.104 642.296 512.565 622.889 521.657 616.171C523.453 614.843 525.247 614.474 527.303 613.818C531.835 612.374 534.288 613.987 538.433 613.577C541.783 613.246 542.704 609.733 546.304 608.186C548.661 607.174 551.933 607.252 554.411 607.15C567.379 606.617 580.388 606.622 593.439 605.777C599.526 605.383 606.098 606.347 612.061 605.307C614.853 604.819 614.452 602.961 617.096 604.591C619.833 606.279 618.651 612.521 618.513 615.01C617.714 629.403 616.857 644.839 609.147 657.422C602.093 668.935 588.212 670.113 575.854 670.675Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M47.4667 674.64C50.6857 674.605 53.9037 674.571 57.1117 674.538C53.8887 674.573 50.6777 674.606 47.4667 674.64Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M941.295 367.764C941.289 364.672 941.282 361.58 941.275 358.494C941.281 361.588 941.288 364.676 941.295 367.764Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M122.468 380.461C122.458 383.473 122.448 386.485 122.438 389.49C122.447 386.476 122.457 383.468 122.468 380.461Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M390.037 671.226C392.598 671.23 395.159 671.233 397.708 671.24C395.143 671.237 392.591 671.232 390.037 671.226Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M234.54 670.611C237.101 670.619 239.661 670.627 242.21 670.637C239.645 670.63 237.093 670.62 234.54 670.611Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M794.45 545.52C794.338 547.553 794.224 549.585 794.112 551.614C794.225 549.579 794.338 547.55 794.45 545.52Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M937.435 660.981C935.456 660.96 933.477 660.94 931.498 660.919C933.486 660.09 935.465 660.127 937.435 660.981Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M761.729 669.983C763.15 669.961 764.57 669.939 765.977 669.915C764.553 669.936 763.141 669.96 761.729 669.983Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M940.524 392.851C940.559 391.314 940.594 389.778 940.629 388.247C940.594 389.785 940.559 391.318 940.524 392.851Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M807.693 198.461C807.576 196.876 807.459 195.292 807.341 193.711C807.459 195.297 807.576 196.879 807.693 198.461Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M941.226 373.816C941.245 372.227 941.264 370.639 941.283 369.056C941.264 370.646 941.245 372.231 941.226 373.816Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M494.541 668.012C495.804 668.031 497.067 668.049 498.319 668.065C497.054 668.045 495.797 668.029 494.541 668.012Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M819.847 670.603C821.081 670.627 822.316 670.652 823.539 670.678C822.3 670.654 821.074 670.628 819.847 670.603Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M953.2 558.893C953.111 557.499 953.022 556.106 952.935 554.715C953.024 556.109 953.112 557.501 953.2 558.893Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M931.037 671.958C932.042 671.952 933.047 671.946 934.04 671.944C933.032 671.951 932.034 671.955 931.037 671.958Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M794.119 670.667C795.114 670.643 796.109 670.617 797.094 670.594C796.095 670.619 795.106 670.644 794.119 670.667Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M570.431 669.907C571.527 669.901 572.622 669.895 573.707 669.887C572.609 669.893 571.52 669.9 570.431 669.907Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M317.822 669.902C318.863 669.909 319.905 669.917 320.935 669.926C319.89 669.92 318.856 669.911 317.822 669.902Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M256.371 670.071C257.433 670.012 258.493 669.952 259.545 669.894C258.48 669.954 257.426 670.012 256.371 670.071Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M550.576 265.453C548.996 265.317 547.416 265.181 545.836 265.044C547.418 265.155 549.091 264.219 550.576 265.453Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M953.35 661.582C951.792 661.425 950.234 661.267 948.676 661.109C950.235 661.267 951.792 661.425 953.35 661.582Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M844.678 275.045C844.574 276.41 844.468 277.776 844.364 279.137C844.469 277.77 844.574 276.407 844.678 275.045Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M941.617 331.958C941.723 330.664 941.829 329.37 941.935 328.081C941.829 329.377 941.722 330.667 941.617 331.958Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M844.057 296.22C843.945 297.583 843.832 298.945 843.721 300.303C843.833 298.939 843.945 297.579 844.057 296.22Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M940.957 384.195C940.947 382.878 940.937 381.561 940.928 380.248C941.778 381.561 941.744 382.875 940.957 384.195Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M794.473 573.327C794.461 574.647 794.45 575.968 794.436 577.282C793.564 575.951 793.738 574.636 794.473 573.327Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M893.775 420.605C893.623 419.269 893.47 417.932 893.318 416.6C893.471 417.938 893.623 419.272 893.775 420.605Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M71.5097 297.975C70.4767 299.34 69.0697 298.405 67.8417 298.571C69.0657 298.372 70.2877 298.174 71.5097 297.975Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M582.302 606.723C580.997 606.725 579.692 606.726 578.387 606.728C579.693 606.726 580.997 606.724 582.302 606.723Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M790.799 303.584C790.921 304.694 791.042 305.804 791.164 306.91C791.042 305.798 790.921 304.691 790.799 303.584Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M145.851 193.342C144.539 193.368 143.227 193.394 141.915 193.42C143.213 192.656 144.521 192.435 145.851 193.342Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M844.025 315.42C843.94 316.584 843.855 317.747 843.771 318.906C843.856 317.742 843.94 316.581 844.025 315.42Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M808.046 172.538C808.148 171.477 808.249 170.416 808.35 169.361C808.248 170.424 808.148 171.481 808.046 172.538Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M797.736 169.214C797.633 170.365 797.53 171.517 797.428 172.664C797.531 171.511 797.633 170.362 797.736 169.214Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M227.843 92.3228C226.508 92.4848 225.173 92.6468 223.839 92.8078C225.174 92.6458 226.508 92.4848 227.843 92.3228Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M131.419 194.259C130.049 194.399 128.679 194.539 127.31 194.679C128.68 194.539 130.05 194.399 131.419 194.259Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M882.152 197.633C882.236 196.562 882.319 195.49 882.402 194.425C882.319 195.497 882.235 196.565 882.152 197.633Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M86.453 374.389C87.242 374.401 88.03 374.412 88.807 374.424C88.015 374.412 87.234 374.401 86.453 374.389Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M797.776 506.574C797.697 507.76 797.618 508.945 797.539 510.128C797.619 508.942 797.698 507.758 797.776 506.574Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M878.262 275.773C878.23 274.63 878.199 273.488 878.166 272.348C878.2 273.492 878.231 274.632 878.262 275.773Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M261.449 89.6183C260.232 89.6183 259.015 89.6193 257.8 89.6193C259.017 88.7833 260.233 88.8043 261.449 89.6183Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M586.36 358.642C585.027 358.676 583.694 358.71 582.363 358.744C583.672 357.818 585.006 357.795 586.36 358.642Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M702.85 667.966C703.64 667.982 704.431 667.999 705.211 668.018C704.417 668.003 703.633 667.984 702.85 667.966Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M732.606 669.354C733.395 669.326 734.184 669.298 734.962 669.271C734.17 669.299 733.388 669.327 732.606 669.354Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M266.911 669.903C267.734 669.924 268.557 669.945 269.369 669.965C268.543 669.944 267.727 669.924 266.911 669.903Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M952.517 545.668C952.4 544.538 952.283 543.407 952.165 542.282C952.283 543.414 952.4 544.541 952.517 545.668Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M845.033 355.17C845.127 356.247 845.22 357.324 845.312 358.394C845.219 357.316 845.126 356.243 845.033 355.17Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M886.396 336.68C886.268 335.538 886.14 334.396 886.013 333.258C886.141 334.401 886.268 335.54 886.396 336.68Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M273.588 670.584C274.418 670.571 275.249 670.558 276.07 670.547C275.236 670.561 274.412 670.572 273.588 670.584Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M575.854 670.675C576.662 670.638 577.468 670.602 578.266 670.566C577.457 670.603 576.655 670.639 575.854 670.675Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M879.645 253.291C879.608 252.134 879.572 250.977 879.536 249.822C879.573 250.98 879.609 252.135 879.645 253.291Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M194.885 671.34C195.695 671.334 196.504 671.327 197.303 671.322C196.489 671.329 195.688 671.335 194.885 671.34Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M879.307 671.329C880.139 671.337 880.972 671.346 881.794 671.355C880.957 671.347 880.132 671.338 879.307 671.329Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M897.282 671.23C898.086 671.287 898.889 671.345 899.683 671.4C898.876 671.343 898.079 671.287 897.282 671.23Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M904.542 671.216C905.36 671.24 906.179 671.265 906.987 671.283C906.166 671.258 905.354 671.237 904.542 671.216Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M941.817 341.266C941.763 340.128 941.71 338.989 941.657 337.854C941.711 338.994 941.763 340.13 941.817 341.266Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M392.484 656.299C394.973 656.17 397.463 656.04 399.953 655.91C397.463 656.04 394.973 656.169 392.484 656.299Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M242.295 242.501C244.456 242.689 246.617 242.877 248.776 243.063C246.615 242.876 244.455 242.688 242.295 242.501Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M852.442 336.453C852.509 338.515 852.578 340.577 852.645 342.633C852.577 340.57 852.509 338.512 852.442 336.453Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M897.04 659.825C899.296 659.962 901.551 660.098 903.807 660.235C901.537 660.338 899.233 660.985 897.04 659.825Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M883.69 415.918C883.707 413.951 883.724 411.984 883.74 410.022C883.723 411.99 883.707 413.954 883.69 415.918Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M877.916 477.719C879.895 477.745 881.876 477.772 883.854 477.798C881.874 477.771 879.895 477.745 877.916 477.719Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M379.632 443.138C379.582 444.637 379.533 446.136 379.486 447.628C379.536 446.127 379.584 444.633 379.632 443.138Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M910.859 379.664C910.778 381.156 910.697 382.647 910.618 384.131C910.7 382.638 910.78 381.151 910.859 379.664Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M868.63 653.187C870.07 653.089 871.51 652.992 872.947 652.899C871.864 653.766 870.775 653.838 868.63 653.187Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M858.993 458.188C859.092 459.796 859.191 461.404 859.289 463.007C859.19 461.398 859.092 459.793 858.993 458.188Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M320.578 204.384C320.593 202.831 320.608 201.278 320.624 199.731C320.609 201.286 320.594 202.835 320.578 204.384Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M860.736 478.044L866.103 477.927C864.332 478.783 862.543 478.813 860.736 478.044Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M910.251 489.504C908.985 489.515 907.72 489.525 906.463 489.538C907.732 489.528 908.992 489.516 910.251 489.504Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M446.779 653.805C448.531 653.828 450.283 653.851 452.035 653.873C450.27 654.758 448.516 654.883 446.779 653.805Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M853.059 349.697C853.146 351.311 853.233 352.925 853.32 354.534C853.233 352.918 853.146 351.308 853.059 349.697Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M285.517 246.291C287.016 245.552 288.527 245.428 290.053 246.211C288.54 246.238 287.029 246.265 285.517 246.291Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M804.026 368.334C804.15 369.695 804.274 371.057 804.398 372.414C804.273 371.051 804.15 369.692 804.026 368.334Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M930.653 336.566C929.883 335.234 929.91 333.909 930.691 332.596C930.678 333.921 930.666 335.244 930.653 336.566Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M853.305 290.326C853.305 291.673 853.306 293.021 853.306 294.364C853.305 293.014 853.305 291.67 853.305 290.326Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M907.039 477.075C908.582 476.602 910.125 475.933 911.661 477.091C910.12 477.086 908.58 477.08 907.039 477.075Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M195.589 655.692C197.011 655.067 198.43 654.739 199.831 655.74C198.416 655.723 197.003 655.708 195.589 655.692Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M486.452 653.687C487.973 653.023 489.49 652.658 490.991 653.734C489.477 653.718 487.965 653.703 486.452 653.687Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M147.964 456.961C148.078 458.334 148.193 459.707 148.306 461.076C148.191 459.702 148.077 458.332 147.964 456.961Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M500.988 377.878C502.484 377.334 503.975 377.05 505.444 377.959C503.958 377.931 502.473 377.905 500.988 377.878Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M853.036 418.748C853.119 420.09 853.202 421.432 853.284 422.771C853.201 421.428 853.118 420.088 853.036 418.748Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M146.997 297.656C146.873 298.737 146.749 299.819 146.626 300.894C146.75 299.811 146.873 298.734 146.997 297.656Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M60.285 611.147C60.208 612.2 60.132 613.252 60.056 614.298C60.135 613.244 60.21 612.195 60.285 611.147Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M884.353 425.85C884.269 424.762 884.185 423.674 884.101 422.591C884.185 423.681 884.269 424.766 884.353 425.85Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M379.127 452.396C379.376 453.188 379.625 453.982 379.874 454.775C378.211 455.177 377.962 454.383 379.127 452.396Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M479.079 376.087C480.433 376.081 481.787 376.076 483.141 376.07C481.789 376.712 480.436 377.153 479.079 376.087Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M148.723 463.462C148.769 464.662 148.815 465.863 148.859 467.059C148.812 465.858 148.767 464.66 148.723 463.462Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M807.313 476.115C807.361 477.22 807.41 478.325 807.459 479.424C807.412 478.316 807.362 477.216 807.313 476.115Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M135.301 658.352C136.586 657.499 137.874 657.573 139.164 658.338C137.876 658.343 136.588 658.348 135.301 658.352Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M169.139 282.219C169.208 283.347 169.278 284.476 169.35 285.597C169.281 284.467 169.21 283.343 169.139 282.219Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M128.615 658.469C129.951 658.474 131.287 658.48 132.622 658.486C131.282 659.398 129.947 659.441 128.615 658.469Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M868.403 264.512C868.414 263.366 868.424 262.221 868.434 261.078C868.423 262.225 868.413 263.368 868.403 264.512Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M255.054 244.471C256.332 244.638 257.61 244.805 258.887 244.971C257.609 244.804 256.331 244.638 255.054 244.471Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M142.278 343.37C142.295 344.44 142.312 345.511 142.332 346.576C142.315 345.504 142.297 344.437 142.278 343.37Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M877.007 343.217C876.937 342.127 876.867 341.037 876.797 339.952C876.867 341.044 876.938 342.131 877.007 343.217Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M578.934 657.191C580.511 657.334 582.088 657.477 583.664 657.619C582.087 657.476 580.511 657.334 578.934 657.191Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M573.666 616.368C572.813 616.382 571.962 616.395 571.117 616.413C571.972 616.402 572.819 616.385 573.666 616.368Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M573.738 656.566C575.06 656.696 576.384 656.827 577.706 656.957C576.383 656.826 575.06 656.696 573.738 656.566Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M335.717 567.498C337.438 577.222 322.326 586.732 321.128 572.909C320.69 567.854 324.07 564.3 327.855 561.21C333.227 556.825 336.228 561.024 335.717 567.498Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M169.144 448.86C172.704 450.317 199.903 459.18 185.775 463.638C182.035 464.818 176.382 464.994 173.14 462.915C169.24 460.413 165.306 453.03 169.144 448.86Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M175.838 317.176C185.07 316.104 193.576 331.267 182.194 330.775C173.909 330.416 162.767 317.176 175.838 317.176Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M208.863 354.355C216.572 361.081 202.283 376.134 196.61 370.308C193.713 367.332 196.385 360.921 198.776 358.431C201.23 355.876 205.596 355.396 208.863 354.355Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M231.813 528.724C227.262 529.447 207.248 535.987 211.422 524.108C213.014 519.578 217.454 520.922 221.513 521.13C228.055 521.464 231.813 521.151 231.813 528.724Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M218.63 559.321C225.443 562.966 215.484 583.364 209.837 579.024C202.002 573.002 212.718 561.153 218.63 559.321Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M248.344 542.923C251.657 541.57 267.274 556.592 261.329 560.771C258.282 562.913 252.509 559.241 250.672 557.027C247.228 552.875 246.916 547.753 248.344 542.923Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M158.241 420.138C161.627 420.694 167.473 420.87 167.692 425.628C167.884 429.81 162.641 431.165 158.95 431.493C155.12 431.833 150.05 430.752 149.977 425.937C149.9 420.742 158.121 421.055 158.241 420.138Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M241.739 627.19C245.345 629.379 262.165 637.261 254.955 642.955C252.257 645.086 244.078 640.685 242.237 638.448C240.04 635.778 237.893 629.523 241.739 627.19Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M288.497 569.997C287.976 561.297 302.654 557.834 299.611 570.183C298.753 573.665 297.923 578.95 293.618 579.152C288.53 579.391 288.346 573.371 288.497 569.997Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M243.155 418.706C243.257 425.306 230.611 431.126 230.513 420.665C230.426 411.317 243.627 408.581 243.155 418.706Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M234.805 384.333C237.921 385.337 242.062 385.853 242.336 389.812C242.627 394.003 238.735 395.868 235.233 395.633C232.361 395.44 228.111 393.314 227.589 390.198C226.824 385.624 231.288 385.325 234.805 384.333Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M176.862 505.165C176.809 499.719 187.723 493.174 189.733 499.493C190.376 501.515 185.232 510.582 183.521 511.261C178.994 513.058 178.362 508.87 176.862 505.165Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M266.297 595.808C262.609 599.109 251.546 608.382 252.373 595.934C252.901 587.982 267.27 588.397 266.297 595.808Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M446.54 631.386C447.749 626.152 465.057 635.232 459.281 641.843C457.343 644.062 452.274 642.054 450.325 640.739C446.468 638.136 446.384 635.733 446.54 631.386Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M350.105 519.348C350.78 517.015 351.53 512.889 353.756 511.4C357.784 508.706 361.291 512.77 361.292 517.053C361.293 525.043 352.198 527.563 350.105 519.348Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M307.208 612.766C311.702 613.796 321.708 624.72 315.905 629.176C310.32 633.465 302.892 616.029 307.208 612.766Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M311.486 530.078C317.264 530.539 325.206 539.896 316.225 541.383C309.2 542.547 301.03 532.37 311.486 530.078Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M363.796 573.136C365.245 568.255 379.575 573.485 378.77 578.859C378.342 581.716 372.483 582.387 369.403 581.632C364.869 580.519 363.692 577.545 363.796 573.136Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M194.879 300.53C194.983 294.22 207.108 291.648 207.996 298.007C208.799 303.752 194.727 309.865 194.879 300.53Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M359.79 638.154C360.664 634.981 361.041 631.162 365.004 630.744C369.542 630.265 369.408 633.745 368.875 637.351C367.423 647.162 361.879 647.652 359.79 638.154Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M168.314 375.605C161.993 377.031 150.416 364.857 157.492 362.245C162.478 360.402 168.314 371.932 168.314 375.605Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M248.936 470.169C245.719 471.684 244.978 472.603 242.507 470.117C240.355 467.952 240.45 461.92 242.297 459.668C249.371 451.044 249.205 466.679 248.936 470.169Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M275.182 452.677C278.537 453.104 288.506 462.56 283.315 466.318C276.954 470.922 273.355 456.499 275.182 452.677Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M347.77 455.168C345.331 464.559 339.244 458.24 339.589 451.134C340.143 439.752 346.451 449.665 347.77 455.168Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M384.782 605.994C388.908 607.701 394.508 623.473 388.615 624.319C381.996 625.268 384.2 609.278 384.782 605.994Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M333.257 597.133C337.978 595.54 339.708 605.499 336.964 607.349C330.927 611.419 332.2 599.483 333.257 597.133Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M288.497 569.997C288.437 571.319 288.378 572.641 288.316 573.955C288.375 572.631 288.435 571.314 288.497 569.997Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M446.54 631.386C446.491 632.742 446.442 634.097 446.391 635.446C446.438 634.088 446.489 632.737 446.54 631.386Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M363.796 573.136C363.77 574.286 363.742 575.436 363.712 576.579C363.738 575.426 363.768 574.281 363.796 573.136Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M885.993 553.344C895.781 553.03 906.7 549.346 916.318 550.518C918.966 550.841 925.061 551.508 920.819 555.435C918.79 557.313 909.077 556.342 906.137 556.965C888.166 560.77 867.766 563.224 849.339 562.907C840.713 562.759 832.023 562.068 823.401 561.612C814.785 561.156 813.938 557.655 823.633 556.541C833.702 555.385 844.034 555.814 854.153 555.802C864.791 555.788 875.376 554.054 885.993 553.344Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M907.481 529.763C906.539 532.445 905.712 538.601 902.102 539.005C897.046 539.57 896.209 530.889 896.395 527.714C896.621 523.882 898 517.516 902.763 518.844C906.786 519.964 907.189 526.696 907.481 529.763Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M876.872 527.799C877.611 524.927 877.974 518.451 882.036 518.231C886.576 517.986 887.873 524.733 887.843 527.966C887.813 531.035 886.678 536.832 883.063 537.541C878.162 538.502 877.724 531.081 876.872 527.799Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M825.896 532.951C824.425 536.403 823.452 541.158 818.9 538.804C815.38 536.984 814.889 529.643 815.553 526.282C816.241 522.8 817.698 518.806 821.699 520.372C826.313 522.179 825.847 529.15 825.896 532.951Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M864.499 529.763C860.16 542.916 852.246 534.017 855.251 524.197C858.393 513.933 864.1 523.902 864.499 529.763Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M843.358 530.413C842.153 532.729 840.871 537.725 837.623 537.199C833.641 536.553 833.513 528.89 833.95 526.036C834.331 523.538 835.453 519.375 838.786 519.429C843.602 519.507 843.279 527.415 843.358 530.413Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M929.91 532.939C926.68 540.186 922.237 533.943 923.659 528.796C925.768 521.163 929.727 527.926 929.91 532.939Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M850.806 555.812C847.279 555.798 843.75 555.785 840.223 555.772C843.753 555.107 847.283 554.848 850.806 555.812Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M920.907 550.489C917.847 550.509 914.788 550.528 911.73 550.548C914.784 549.632 917.843 549.612 920.907 550.489Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M825.896 532.951C825.874 531.193 825.85 529.435 825.829 527.681C826.734 529.429 826.671 531.185 825.896 532.951Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M845.091 562.87C846.511 562.883 847.931 562.895 849.339 562.908C847.915 562.895 846.503 562.883 845.091 562.87Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M821.245 561.613C822.683 561.613 824.122 561.612 825.55 561.613C824.106 561.613 822.676 561.613 821.245 561.613Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M833.037 555.963C831.037 556.127 829.037 556.291 827.038 556.455C829.038 556.291 831.037 556.127 833.037 555.963Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M860.077 555.79C858.102 555.794 856.128 555.798 854.153 555.802C856.127 554.97 858.101 554.946 860.077 555.79Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M843.358 530.413C843.309 528.581 843.261 526.749 843.215 524.919C843.264 526.752 843.311 528.583 843.358 530.413Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M815.554 526.282C815.543 528.069 815.532 529.856 815.518 531.636C815.529 529.847 815.541 528.065 815.554 526.282Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M897.214 552.679C895.468 552.837 893.723 552.994 891.979 553.152C893.725 552.994 895.47 552.837 897.214 552.679Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M929.91 532.939C929.853 531.378 929.797 529.817 929.741 528.258C929.798 529.82 929.853 531.379 929.91 532.939Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M855.641 562.922C856.856 562.899 858.07 562.876 859.273 562.855C858.054 562.879 856.848 562.901 855.641 562.922Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M907.481 529.763C907.374 528.641 907.267 527.518 907.161 526.4C907.268 527.524 907.375 528.643 907.481 529.763Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M864.499 529.763C864.421 528.611 864.342 527.458 864.264 526.309C864.343 527.462 864.421 528.613 864.499 529.763Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M237.863 191.233C233.084 189.979 232.188 183.263 232.956 179.138C233.827 177.15 234.698 175.163 235.569 173.175C235.228 172.44 234.888 171.705 234.548 170.97C234.985 166.876 233.868 165.097 238.573 166.755C247.313 169.837 248.71 190.917 237.863 191.233Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M308.604 178.248C312.541 177.938 319.692 182.523 315.773 186.959C313.345 189.707 305.344 188.407 302.08 188.981C297.751 189.742 293.236 190.783 295.578 184.299C297.417 179.207 303.985 178.115 308.604 178.248Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M304.462 208.746C300.463 205.539 302.248 190.932 308.334 193.56C314.595 196.265 307.799 206.638 304.462 208.746Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M232.956 179.138C232.956 180.675 232.955 182.213 232.952 183.743C232.118 182.203 232.144 180.67 232.956 179.138Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M237.863 191.233C238.678 191.21 239.494 191.186 240.299 191.165C239.48 191.191 238.672 191.212 237.863 191.233Z'
        fill={theme.palette.text.primary}
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M731.645 611.899C731.271 618.543 724.136 613.631 724.642 609.186C725.561 601.12 730.122 609.091 731.645 611.899Z'
        fill={theme.palette.text.primary}
      />
    </svg>
  )
}

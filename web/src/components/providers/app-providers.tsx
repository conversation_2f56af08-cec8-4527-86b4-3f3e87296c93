import * as React from 'react'
import { Toaster } from 'sonner'

import { AuthProvider } from '@/contexts/auth'
import { ConfigProvider } from '@/contexts/config'
import { ACCESS_TOKEN_KEY } from '@/env'
import { queryClient } from '@/hooks/api/react-query'
import { createLocalStorageStrategy } from '@/lib/auth'
import { ThemeCustomization } from '@/theme'
import { NotistackProvider } from '../third-party/notistack'
import { ReactQueryProvider } from './react-query-provider'

const jwtStrategy = createLocalStorageStrategy(ACCESS_TOKEN_KEY)

export const AppProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ConfigProvider>
      <ThemeCustomization>
        <NotistackProvider>
          <ReactQueryProvider client={queryClient}>
            <AuthProvider ACCESS_TOKEN_KEY={ACCESS_TOKEN_KEY} strategy={jwtStrategy}>
              {children}
              <Toaster />
            </AuthProvider>
          </ReactQueryProvider>
        </NotistackProvider>
      </ThemeCustomization>
    </ConfigProvider>
  )
}

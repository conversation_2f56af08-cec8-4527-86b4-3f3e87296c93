import * as React from 'react'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Fade from '@mui/material/Fade'
import InputAdornment from '@mui/material/InputAdornment'
import List from '@mui/material/List'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemText from '@mui/material/ListItemText'
import ListSubheader from '@mui/material/ListSubheader'
import OutlinedInput from '@mui/material/OutlinedInput'
import Popper from '@mui/material/Popper'
import Stack from '@mui/material/Stack'
import { useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { IconCommand, IconSearch } from '@tabler/icons-react'

import { EmptySearch } from '@/components/design-systems/empty-search'
import { MainCard } from '@/components/design-systems/main-card'
import { NotificationItem } from '@/components/design-systems/notification-item'
import { ThemeDirection } from '@/config'
import { AvatarSize } from '@/theme/enum/avatar'

const profileData = [
  {
    alt: 'Aplican Warner',
    src: '/assets/images/users/avatar-1.png',
    title: 'Aplican Warner',
    subTitle: 'Admin'
  },
  {
    alt: 'Apliaye Aweoa',
    src: '/assets/images/users/avatar-2.png',
    title: 'Apliaye Aweoa',
    subTitle: 'Admin'
  }
]

const listContent = [
  { title: 'Role', items: ['Applican', 'App User'] },
  { title: 'Files', items: ['Applican', 'Applican'] }
]

export const SearchBar = () => {
  const theme = useTheme()
  const downSM = useMediaQuery(theme.breakpoints.down('sm'))

  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null)
  const [isEmptySearch, setIsEmptySearch] = React.useState(true)
  const [isPopperOpen, setIsPopperOpen] = React.useState(false)
  const inputRef = React.useRef<HTMLInputElement>(null)

  const buttonStyle = { borderRadius: 2, p: 1 }

  const openPopper = () => {
    setAnchorEl(inputRef.current)
    setIsPopperOpen(true)
  }

  const handleActionClick = () => {
    if (isPopperOpen) {
      setIsPopperOpen(false)
      setAnchorEl(null)
    } else {
      openPopper()
    }
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isEmpty = event.target.value.trim() === ''
    setIsEmptySearch(isEmpty)
    if (!isPopperOpen && !isEmpty) {
      openPopper()
    }
  }

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !isPopperOpen) {
      openPopper()
    } else if (event.key === 'Escape' && isPopperOpen) {
      setIsPopperOpen(false)
      setAnchorEl(null)
    } else if (event.ctrlKey && event.key === 'k') {
      event.preventDefault()
      if (!isPopperOpen) {
        openPopper()
      }
    }
  }

  const renderSubheader = (title: string, withMarginTop = false) => (
    <ListSubheader
      sx={{
        color: 'text.disabled',
        typography: 'caption',
        py: 0.5,
        px: 1,
        mb: 0.5,
        ...(withMarginTop && { mt: 1.5 })
      }}
    >
      {title}
    </ListSubheader>
  )

  const renderListItem = (item: string, index: number) => (
    <ListItemButton key={index} sx={buttonStyle} onClick={handleActionClick}>
      <ListItemText primary={item} />
    </ListItemButton>
  )

  React.useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'k') {
        event.preventDefault()
        if (document.activeElement !== inputRef.current) {
          openPopper()
          inputRef.current?.focus()
        }
      }
    }

    window.addEventListener('keydown', handleGlobalKeyDown)
    return () => window.removeEventListener('keydown', handleGlobalKeyDown)
  }, [isPopperOpen])

  return (
    <React.Fragment>
      <OutlinedInput
        inputRef={inputRef}
        placeholder='Search here'
        startAdornment={
          <InputAdornment position='start'>
            <IconSearch />
          </InputAdornment>
        }
        endAdornment={
          <InputAdornment position='end'>
            <Stack
              direction='row'
              sx={{
                'gap': 0.25,
                'opacity': 0.8,
                'alignItems': 'center',
                'color': 'grey.600',
                '& svg': { color: 'inherit' }
              }}
            >
              <IconCommand />
              <Typography variant='caption'>+ K</Typography>
            </Stack>
          </InputAdornment>
        }
        aria-describedby='Search'
        slotProps={{ input: { 'aria-label': 'search' } }}
        onClick={handleActionClick}
        onKeyDown={handleKeyDown}
        onChange={handleInputChange}
        sx={{ minWidth: { xs: 200, sm: 240 } }}
      />
      <Popper
        placement='bottom'
        id={isPopperOpen ? 'search-action-popper' : undefined}
        open={isPopperOpen}
        anchorEl={anchorEl}
        transition
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [downSM ? (theme.direction === ThemeDirection.RTL ? -20 : 20) : 0, 8]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Fade in={isPopperOpen} {...TransitionProps}>
            <MainCard
              sx={{
                borderRadius: 2,
                boxShadow: theme.customShadows.tooltip,
                width: 1,
                minWidth: { xs: 352, sm: 240 },
                maxWidth: { xs: 352, md: 420 },
                p: 0.5
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setIsPopperOpen(false)
                  setAnchorEl(null)
                }}
              >
                {isEmptySearch ? (
                  <EmptySearch />
                ) : (
                  <List disablePadding>
                    {renderSubheader('Users')}
                    {profileData.map((user, index) => (
                      <ListItemButton sx={buttonStyle} key={index} onClick={handleActionClick}>
                        <NotificationItem
                          avatar={{ alt: user.alt, src: user.src, size: AvatarSize.XS }}
                          title={user.title}
                          subTitle={user.subTitle}
                        />
                      </ListItemButton>
                    ))}
                    {listContent.map((list, i) => (
                      <React.Fragment key={i}>
                        {renderSubheader(list.title, true)}
                        {list.items.map((item, index) => renderListItem(item, index))}
                      </React.Fragment>
                    ))}
                  </List>
                )}
              </ClickAwayListener>
            </MainCard>
          </Fade>
        )}
      </Popper>
    </React.Fragment>
  )
}

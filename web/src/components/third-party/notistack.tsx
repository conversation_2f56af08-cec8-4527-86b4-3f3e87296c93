import * as React from 'react'
import Fade from '@mui/material/Fade'
import Grow from '@mui/material/Grow'
import Slide from '@mui/material/Slide'
import { styled } from '@mui/material/styles'
import Zoom from '@mui/material/Zoom'
import {
  IconAlertTriangle,
  IconBug,
  IconChecks,
  IconInfoCircle,
  IconSpeakerphone
} from '@tabler/icons-react'
import { SnackbarProvider } from 'notistack'

import { useSnackbarStore } from '@/stores/snackbar'
import { Loader } from '../design-systems/loader'

// Props 类型定义
interface NotistackProps {
  children: React.ReactNode
}

// 样式封装
const StyledSnackbarProvider = styled(SnackbarProvider)(({ theme }) => ({
  '&.notistack-MuiContent': {
    color: theme.palette.background.default
  },
  '&.notistack-MuiContent-default': {
    backgroundColor: theme.palette.primary.main
  },
  '&.notistack-MuiContent-error': {
    backgroundColor: theme.palette.error.main
  },
  '&.notistack-MuiContent-success': {
    backgroundColor: theme.palette.success.main
  },
  '&.notistack-MuiContent-info': {
    backgroundColor: theme.palette.info.main
  },
  '&.notistack-MuiContent-warning': {
    backgroundColor: theme.palette.warning.main
  },
  '& #notistack-snackbar': {
    gap: 8
  }
}))

// 动画定义
const TransitionSlideLeft = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='left' />
)

const TransitionSlideUp = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='up' />
)

const TransitionSlideRight = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='right' />
)

const TransitionSlideDown = (props: React.ComponentProps<typeof Slide>) => (
  <Slide {...props} direction='down' />
)

const GrowTransition = (props: React.ComponentProps<typeof Grow>) => <Grow {...props} />

const ZoomTransition = (props: React.ComponentProps<typeof Zoom>) => <Zoom {...props} />

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const animation: Record<string, any> = {
  SlideLeft: TransitionSlideLeft,
  SlideUp: TransitionSlideUp,
  SlideRight: TransitionSlideRight,
  SlideDown: TransitionSlideDown,
  Grow: GrowTransition,
  Zoom: ZoomTransition,
  Fade
}

const iconSX: React.CSSProperties = { fontSize: '1.15rem' }

export const NotistackProvider = ({ children }: NotistackProps) => {
  const snackbar = useSnackbarStore()

  if (snackbar === undefined) {
    return <Loader />
  }

  return (
    <StyledSnackbarProvider
      maxSnack={snackbar.maxStack}
      dense={snackbar.dense}
      anchorOrigin={snackbar.anchorOrigin}
      TransitionComponent={animation[snackbar.transition]}
      iconVariant={
        snackbar.iconVariant === 'useemojis'
          ? {
              default: <IconSpeakerphone style={iconSX} />,
              success: <IconChecks style={iconSX} />,
              error: <IconBug style={iconSX} />,
              warning: <IconAlertTriangle style={iconSX} />,
              info: <IconInfoCircle style={iconSX} />
            }
          : undefined
      }
      hideIconVariant={snackbar.iconVariant === 'hide'}
    >
      {children}
    </StyledSnackbarProvider>
  )
}

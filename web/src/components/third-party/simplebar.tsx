import * as React from 'react'
import { BrowserView, MobileView } from 'react-device-detect'
import Box from '@mui/material/Box'
import type { SxProps, Theme } from '@mui/material/styles'
import { alpha, styled, useTheme } from '@mui/material/styles'
import MainSimpleBar from 'simplebar-react'

import { ThemeDirection, ThemeMode } from '@/config'

// Root 样式封装 BrowserView
const RootStyle = styled(BrowserView)({
  flexGrow: 1,
  height: '100%',
  overflow: 'hidden'
})

// 封装滚动条样式
const SimpleBarStyle = styled(MainSimpleBar)(({ theme }: { theme: Theme }) => ({
  'maxHeight': '100%',
  '& .simplebar-scrollbar': {
    '&:before': {
      background: alpha(theme.palette.grey[theme.palette.mode === ThemeMode.DARK ? 200 : 500], 0.48)
    },
    '&.simplebar-visible:before': {
      opacity: 1
    }
  },
  '& .simplebar-track.simplebar-vertical': { width: 10 },
  '& .simplebar-track.simplebar-horizontal .simplebar-scrollbar': { height: 6 },
  '& .simplebar-mask': { zIndex: 'inherit' }
}))

// 定义 props 类型
interface SimpleBarProps extends React.PropsWithChildren, React.HTMLAttributes<HTMLElement> {
  sx?: SxProps<Theme>
}

// 主组件
export const SimpleBar = ({ children, sx, ...other }: SimpleBarProps) => {
  const theme = useTheme()

  return (
    <>
      <RootStyle>
        <SimpleBarStyle
          clickOnTrack={false}
          sx={sx}
          data-simplebar-direction={theme.direction === ThemeDirection.RTL ? 'rtl' : 'ltr'}
          {...other}
        >
          {children}
        </SimpleBarStyle>
      </RootStyle>
      <MobileView>
        <Box sx={{ overflowX: 'auto', ...sx }} {...other}>
          {children}
        </Box>
      </MobileView>
    </>
  )
}

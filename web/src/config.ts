import { ROUTE_PATHS } from './constants/routes'

export const APP_DEFAULT_PATH = ROUTE_PATHS.INDEX.path

export const DRAWER_WIDTH = 254
export const MINI_DRAWER_WIDTH = 76 + 1 // 1px - for right-side border

export enum ThemeDirection {
  LTR = 'ltr',
  RTL = 'rtl'
}

export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark'
}

export enum ThemeI18n {
  EN = 'en',
  ZH = 'zh'
}

export interface Config {
  mode: ThemeMode
  i18n: ThemeI18n
  direction: ThemeDirection
}

export const config: Config = {
  mode: ThemeMode.LIGHT,
  i18n: ThemeI18n.EN,
  direction: ThemeDirection.LTR
}

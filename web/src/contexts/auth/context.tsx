import * as React from 'react'

import type { User } from '@/hooks/api/user'

interface CommonAuthContext {
  login: (accessToken: string) => void
  logout: () => void
}

interface ProcessingCurrentUserContext {
  user: null
  isPending: true
}

interface ProcessedCurrentUserContext {
  user: User
  isPending: false
}

interface AuthenticatedAuthContext extends CommonAuthContext {
  accessToken: string
  isAuthenticated: true
  payload: unknown
}

interface UnauthenticatedAuthContext extends CommonAuth<PERSON>ontext {
  accessToken: null
  isAuthenticated: false
  payload: null
}

export type AuthContextType = (AuthenticatedAuthContext | UnauthenticatedAuthContext) &
  (ProcessingCurrentUserContext | ProcessedCurrentUserContext)

export const AuthContext = React.createContext<AuthContextType>({
  login: () => {
    throw new Error('login 必须在 AuthProvider 内使用')
  },
  logout: () => {
    throw new Error('logout 必须在 AuthProvider 内使用')
  },
  accessToken: null,
  isAuthenticated: false,
  payload: null,
  user: null,
  isPending: true
})

export const useAuth = () => {
  const context = React.useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth 必须在 AuthProvider 内使用')
  }
  return context
}

import * as React from 'react'
import { useQuery } from '@tanstack/react-query'

import { getUserProfileQueryOptions } from '@/hooks/api/user/query-options'
import { useLocalStorageSync } from '@/hooks/use-local-storage-sync'
import type { AuthStrategy } from '@/lib/auth'
import { AuthContext } from './context'

export interface AuthProviderProps {
  children: React.ReactNode
  ACCESS_TOKEN_KEY: string
  strategy: AuthStrategy<unknown>
}

export const AuthProvider = ({ children, ACCESS_TOKEN_KEY, strategy }: AuthProviderProps) => {
  // accessToken
  const [accessToken, setAccessToken] = useLocalStorageSync<string | null>(ACCESS_TOKEN_KEY, null)

  // isAuthenticated
  const isAuthenticated = React.useMemo(() => {
    return !!accessToken && strategy.isAuthenticated()
  }, [ACCESS_TOKEN_KEY, strategy, accessToken])

  // payload
  const payload = React.useMemo<unknown>(() => {
    return strategy.getPayload()
  }, [ACCESS_TOKEN_KEY, strategy, accessToken])

  // user
  const userQueryResult = useQuery({
    ...getUserProfileQueryOptions(accessToken),
    enabled: isAuthenticated
  })
  const [user, isPending] = React.useMemo(() => {
    if (!isAuthenticated) {
      return [null, false]
    }
    if (userQueryResult.isPending) {
      return [null, true]
    }
    if (userQueryResult.isError) {
      setAccessToken(null) // 清除无效的 accessToken
      return [null, false]
    }
    if (userQueryResult.isSuccess) {
      return [userQueryResult.data.data, false]
    }
    // fallback 不应该进入
    console.warn('Unknown query status for user profile:', userQueryResult)
    return [null, false]
  }, [userQueryResult, isAuthenticated])

  // 符合联合类型的 contextValue
  const contextValue = React.useMemo(() => {
    if (!!accessToken && isAuthenticated && !!payload && !!user) {
      return {
        login: (accessToken: string) => setAccessToken(accessToken),
        logout: () => {
          setAccessToken(null)
        },
        accessToken: accessToken,
        isAuthenticated: true as const,
        payload,
        user,
        isPending: false as const
      }
    }

    return {
      login: (accessToken: string) => setAccessToken(accessToken),
      logout: () => {
        setAccessToken(null)
      },
      accessToken: null,
      isAuthenticated: false as const,
      payload: null,
      user: null,
      isPending: true as const
    }
  }, [ACCESS_TOKEN_KEY, accessToken, isAuthenticated, payload, user, isPending])

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
}

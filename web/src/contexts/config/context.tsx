import * as React from 'react'

import { type Config, config } from '@/config'

interface ConfigContextType extends Config {
  setThemeMode: (mode: Config['mode']) => void
  setThemeDirection: (direction: Config['direction']) => void
  setThemeI18n: (i18n: Config['i18n']) => void
}

export const ConfigContext = React.createContext<ConfigContextType>({
  ...config,
  setThemeMode: () => {
    throw new Error('setThemeMode function is not implemented')
  },
  setThemeDirection: () => {
    throw new Error('setThemeDirection function is not implemented')
  },
  setThemeI18n: () => {
    throw new Error('setThemeI18n function is not implemented')
  }
})

export const useConfig = () => {
  return React.useContext(ConfigContext)
}

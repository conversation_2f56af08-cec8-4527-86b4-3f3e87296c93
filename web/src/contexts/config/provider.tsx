import * as React from 'react'

import { config as defaultSiteConfig } from '@/config'
import { CONFIG_KEY } from '@/env'
import { useLocalStorageSync } from '@/hooks/use-local-storage-sync'
import { ConfigContext } from './context'

export const ConfigProvider = ({ children }: { children: React.ReactNode }) => {
  const [config, setConfig] = useLocalStorageSync(CONFIG_KEY, defaultSiteConfig)

  const setThemeMode = (mode: typeof config.mode) => {
    setConfig(prevConfig => ({ ...prevConfig, mode }))
  }

  const setThemeDirection = (direction: typeof config.direction) => {
    setConfig(prevConfig => ({ ...prevConfig, direction }))
  }

  const setThemeI18n = (i18n: typeof config.i18n) => {
    setConfig(prevConfig => ({ ...prevConfig, i18n }))
  }

  const contextValue = React.useMemo(
    () => ({
      ...config,
      setThemeMode,
      setThemeDirection,
      setThemeI18n
    }),
    [config]
  )

  return <ConfigContext.Provider value={contextValue}>{children}</ConfigContext.Provider>
}

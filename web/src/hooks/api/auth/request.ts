import { z } from 'zod'

import { API_AUTH_LOGIN_WITH_PASSWORD } from '@/constants/api-urls'
import { apiRequest, ApiRequestDataError, createApiResponseSchema } from '@/lib/api-request'

// API API_AUTH_LOGIN_WITH_PASSWORD

// Schema
export const LoginWithPasswordRequestDataSchema = z.object({
  username: z.coerce.string().nonempty('用户名不能为空'),
  password: z.coerce.string().nonempty('密码不能为空')
})

export const LoginWithPasswordResponseSchema = createApiResponseSchema(
  z.object({
    token: z.string()
  })
)

export const TokenPayloadSchema = z
  .object({
    user_id: z.string()
  })
  .passthrough()

// Type
export type LoginWithPasswordRequestData = z.infer<typeof LoginWithPasswordRequestDataSchema>
export type LoginWithPasswordResponse = z.infer<typeof LoginWithPasswordResponseSchema>
export type TokenPayload = z.infer<typeof TokenPayloadSchema>

// Request
export const authLoginWithPasswordRequest = async (data: LoginWithPasswordRequestData) => {
  // Validate Request Data
  const parsedData = LoginWithPasswordRequestDataSchema.safeParse(data)
  if (!parsedData.success) {
    throw new ApiRequestDataError(parsedData.error)
  }
  const apiResponse = await apiRequest.post<LoginWithPasswordResponse>(
    API_AUTH_LOGIN_WITH_PASSWORD,
    parsedData.data
  )
  return apiResponse
}

import { z } from 'zod'

import { API_PROJECTS } from '@/constants/api-urls'
import {
  apiRequest,
  ApiResponseDataError,
  createApiResponseSchema,
  wrapPaginationSchema
} from '@/lib/api-request'

// API API_PROJECTS

// Schema
export const ProjectSchema = z.object({
  id: z.string().uuid(),
  created_at: z.string().datetime(),
  created_by: z.string().uuid(),
  host_ip: z.string().ip(),
  host_remark: z.string().max(100),
  file_id: z.string().uuid(),
  scan_status: z.enum(['idle', 'running', 'completed', 'error']),
  scan_started_at: z.string().datetime().nullable(),
  scan_completed_at: z.string().datetime().nullable(),
  scan_error: z.string().max(200).nullable()
})

export const ProjectsResponseSchema = createApiResponseSchema(
  wrapPaginationSchema(
    z.object({
      projects: ProjectSchema.array()
    })
  )
)

// Type
export type Project = z.infer<typeof ProjectSchema>
export type ProjectsResponse = z.infer<typeof ProjectsResponseSchema>

// Request
export const projectsRequest = async () => {
  const apiResponse = await apiRequest.get<ProjectsResponse>(API_PROJECTS)
  // Validate Response Data
  const parsedResponse = ProjectsResponseSchema.safeParse(apiResponse)
  if (!parsedResponse.success) {
    throw new ApiResponseDataError(parsedResponse.error)
  }
  return parsedResponse.data
}

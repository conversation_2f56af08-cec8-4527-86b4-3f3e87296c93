import { queryOptions } from '@tanstack/react-query'

import { API_PROFILE } from '@/constants/api-urls'
import { userProfileRequest } from './request'

// userProfileRequest

// 这里的 queryKey 需要包含 accessToken，以便在 accessToken 变化时重新获取用户信息
// 修复旧用户“回闪”
// 切换用户 → accessToken 更新 → useQuery() 执行：
// enabled: true，立即命中上一个用户的缓存（非 fresh 数据）
// 一两帧后：Query 执行新的 getUserProfile 请求 → 新的 user 替换旧值
export const getUserProfileQueryKey = (accessToken?: string | null) => {
  return [API_PROFILE, accessToken]
}

export const getUserProfileQueryOptions = (accessToken?: string | null) => {
  return queryOptions({
    queryKey: getUserProfileQueryKey(accessToken),
    queryFn: userProfileRequest,
    refetchOnWindowFocus: false
  })
}

import { createFormHook } from '@tanstack/react-form'

import {
  FieldErrorMessage,
  FormSubmitButton,
  FormSubmitErrorMessage
} from '@/components/design-systems/form'
import { fieldContext, formContext } from './form-context'

export const { useAppForm } = createFormHook({
  fieldComponents: {
    ErrorMessage: FieldErrorMessage
  },
  formComponents: {
    SubmitButton: FormSubmitButton,
    SubmitErrorMessage: FormSubmitErrorMessage
  },
  fieldContext,
  formContext
})

import { useAuth } from '@/contexts/auth/context'

export const useCurrentUser = () => {
  const { user, isPending } = useAuth()

  return {
    // 这里为了方便使用，直接断言 user 不为 null
    // 这部分验证逻辑已经在 RoleMiddleware 中处理过了
    // authenticated 中的页面中使用是必定有用户信息的，
    // 如果没有用户信息，RoleMiddleware 会重定向到登录页面
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    user: user!,
    isPending
  }
}

import { type SetStateAction, useCallback, useEffect, useSyncExternalStore } from 'react'

import { setLocalStorage, STORAGE_CHANGE_EVENT } from '@/lib/storage'

const isBrowser = typeof window !== 'undefined' && typeof window.localStorage !== 'undefined'

// 用于缓存 localStorage 的解析结果，防止 getSnapshot 引发无限渲染
const snapshotCache = new Map<string, unknown>()

/**
 * 本地存储同步 Hook（支持多标签页、同页面、函数式更新、SSR 安全）
 * @param key localStorage key
 * @param initialValue 初始值（用于首次挂载或解析失败时 fallback）
 * @returns [当前值, 设置值函数]
 */
export function useLocalStorageSync<T>(
  key: string,
  initialValue: T
): [T, (value: SetStateAction<T>) => void] {
  const subscribe = useCallback(
    (callback: () => void) => {
      if (!isBrowser)
        return () => {
          // SSR 环境不需要订阅
        }

      const handleStorage = (event: StorageEvent) => {
        if (event.key === key) callback()
      }

      const handleCustomChange = (event: CustomEvent<{ key: string }>) => {
        if (event.detail.key === key) callback()
      }

      window.addEventListener('storage', handleStorage)
      window.addEventListener(STORAGE_CHANGE_EVENT, handleCustomChange as EventListener)

      return () => {
        window.removeEventListener('storage', handleStorage)
        window.removeEventListener(STORAGE_CHANGE_EVENT, handleCustomChange as EventListener)
      }
    },
    [key]
  )

  const getSnapshot = useCallback((): T => {
    if (!isBrowser) return initialValue

    try {
      const raw = localStorage.getItem(key)
      if (raw !== null) {
        const parsed = JSON.parse(raw) as T
        const cached = snapshotCache.get(key) as T | undefined

        // 避免返回新引用导致无限更新
        if (cached && JSON.stringify(parsed) === JSON.stringify(cached)) {
          return cached
        }

        snapshotCache.set(key, parsed)
        return parsed
      }
    } catch (e) {
      console.error(`Failed to parse localStorage value for key "${key}":`, e)
    }

    return initialValue
  }, [key, initialValue])

  const value = useSyncExternalStore(subscribe, getSnapshot)

  const setValue = useCallback(
    (newValue: SetStateAction<T>) => {
      const resolved =
        typeof newValue === 'function' ? (newValue as (prev: T) => T)(getSnapshot()) : newValue

      const isNullable = resolved === null || resolved === undefined
      setLocalStorage(key, isNullable ? null : resolved)
    },
    [key, getSnapshot]
  )

  useEffect(() => {
    if (!isBrowser) return

    const existingRaw = localStorage.getItem(key)
    if (existingRaw === null) {
      setLocalStorage(key, initialValue)
      return
    }
  }, [key, initialValue])

  return [value, setValue] as const
}

import * as React from 'react'

import type { NavItemType } from '@/menu/types'

/***************************  MENU COLLAPSED - RECURSIVE FUNCTION  ***************************/

/**
 * Recursively traverses menu items to find and open the correct parent menu.
 * If a menu item matches the current pathname, it marks the corresponding menu as selected and opens it.
 *
 * @param items - List of menu items.
 * @param pathname - Current route pathname.
 * @param menuId - ID of the menu to be set as selected.
 * @param setSelected - Function to update the selected menu.
 * @param setOpen - Function to update the open state.
 */

const setParentOpenedMenu = (
  items: NavItemType[],
  pathname: string,
  menuId: string | undefined,
  setSelected: React.Dispatch<React.SetStateAction<string | null>>,
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
) => {
  for (const item of items) {
    // Recursively check child menus
    if (item.type === 'group' && item.children.length) {
      setParentOpenedMenu(item.children, pathname, menuId, setSelected, setOpen)
    }

    if (item.type === 'item' && item.url === pathname) {
      setSelected(menuId ?? null)
      setOpen(true)
    }
  }
}

/***************************  MENU COLLAPSED - HOOK  ***************************/

/**
 * Hook to handle menu collapse behavior based on the current route.
 * Automatically expands the parent menu of the active route item.
 *
 * @param menu - The menu object containing items.
 * @param pathname - Current route pathname.
 * @param miniMenuOpened - Flag indicating if the mini menu is open.
 * @param setSelected - Function to update selected menu state.
 * @param setOpen - Function to update menu open state.
 * @param setAnchorEl - Function to update the anchor element state.
 */

export const useMenuCollapse = (
  menu: NavItemType,
  pathname: string,
  miniMenuOpened: boolean,
  setSelected: React.Dispatch<React.SetStateAction<string | null>>,
  setOpen: React.Dispatch<React.SetStateAction<boolean>>,
  setAnchorEl?: React.Dispatch<React.SetStateAction<HTMLElement | null>>
) => {
  React.useEffect(() => {
    setOpen(false) // Close the menu initially

    // Reset selection based on menu state
    if (!miniMenuOpened) {
      setSelected(null)
    } else {
      if (setAnchorEl) setAnchorEl(null)
    }

    // If menu has children, determine which should be opened
    if (menu.type === 'group' && menu.children.length) {
      setParentOpenedMenu(menu.children, pathname, menu.id, setSelected, setOpen)
    }
  }, [pathname, menu])
}

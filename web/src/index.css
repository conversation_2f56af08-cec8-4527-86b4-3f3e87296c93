@layer theme, base, mui, components, utilities;
@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@font-face {
  font-family: Noto_Sans_SC;
  src: url('/fonts/Noto_Sans_SC/NotoSansSC-Regular.woff2') format('woff2');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: Noto_Sans_SC;
  src: url('/fonts/Noto_Sans_SC/NotoSansSC-Medium.woff2') format('woff2');
  font-weight: 500;
  font-display: swap;
}
@font-face {
  font-family: Noto_Sans_SC;
  src: url('/fonts/Noto_Sans_SC/NotoSansSC-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-display: swap;
}
@font-face {
  font-family: Noto_Sans_SC;
  src: url('/fonts/Noto_Sans_SC/NotoSansSC-Bold.woff2') format('woff2');
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: Source_Sans_3;
  src: url('/fonts/Source_Sans_3/SourceSans3-Regular.woff2') format('woff2');
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: Source_Sans_3;
  src: url('/fonts/Source_Sans_3/SourceSans3-Medium.woff2') format('woff2');
  font-weight: 500;
  font-display: swap;
}
@font-face {
  font-family: Source_Sans_3;
  src: url('/fonts/Source_Sans_3/SourceSans3-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-display: swap;
}
@font-face {
  font-family: Source_Sans_3;
  src: url('/fonts/Source_Sans_3/SourceSans3-Bold.woff2') format('woff2');
  font-weight: 700;
  font-display: swap;
}

@theme {
  --font-sans: Noto_Sans_SC, sans-serif;
  --font-mono: Source_Sans_3, monospace;
}

@layer base {
  body {
    @apply flex h-screen min-h-svh flex-col;
  }

  #root {
    @apply flex-1;
  }
}

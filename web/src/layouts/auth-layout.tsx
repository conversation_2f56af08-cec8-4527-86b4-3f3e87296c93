import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import { Outlet } from '@tanstack/react-router'

import { Copyright } from '@/components/copyright'
import { IconLogo } from '@/components/icons'

export const AuthLayout = () => {
  return (
    <Stack sx={{ height: '100vh', p: { xs: 3, sm: 7 } }}>
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          display: 'flex',
          justifyContent: 'center',
          m: 1
        }}
      >
        <IconLogo />
      </Box>

      <Stack sx={{ height: 1, alignItems: 'center', justifyContent: 'space-between', gap: 3 }}>
        <Stack sx={{ flexGrow: 1, width: 1, justifyContent: 'center', maxWidth: 458 }}>
          <Outlet />
        </Stack>

        <Copyright />
      </Stack>
    </Stack>
  )
}

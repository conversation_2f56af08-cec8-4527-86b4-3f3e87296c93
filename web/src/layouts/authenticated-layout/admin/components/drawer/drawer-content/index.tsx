import Stack from '@mui/material/Stack'

import { SimpleBar } from '@/components/third-party/simplebar'
import { MINI_DRAWER_WIDTH } from '@/config'
import { MiniDrawer } from './mini-drawer'
import { NavCard } from './nav-card'
import { ResponsiveDrawer } from './responsive-drawer'

export interface DrawerContentProps {
  open: boolean
}

export const DrawerContent = ({ open }: DrawerContentProps) => {
  const isMiniDrawer = !open
  const contentHeight = `calc(100vh - ${MINI_DRAWER_WIDTH}px)`

  return (
    <SimpleBar sx={{ height: contentHeight }}>
      <Stack
        sx={{
          minHeight: contentHeight,
          px: isMiniDrawer ? 0 : 2,
          justifyContent: 'space-between'
        }}
      >
        {isMiniDrawer ? <MiniDrawer /> : <ResponsiveDrawer />}
        <NavCard isMiniDrawer={isMiniDrawer} />
      </Stack>
    </SimpleBar>
  )
}

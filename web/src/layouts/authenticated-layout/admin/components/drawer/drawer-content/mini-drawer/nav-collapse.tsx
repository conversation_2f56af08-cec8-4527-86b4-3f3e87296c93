import * as React from 'react'
import Box from '@mui/material/Box'
import ButtonBase from '@mui/material/ButtonBase'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Grow from '@mui/material/Grow'
import ListItem from '@mui/material/ListItem'
import ListItemAvatar from '@mui/material/ListItemAvatar'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Popper from '@mui/material/Popper'
import { type Theme, useTheme } from '@mui/material/styles'
import { IconChevronRight } from '@tabler/icons-react'
import { useLocation } from '@tanstack/react-router'

import { DynamicIcon } from '@/components/design-systems/dynamic-icon'
import { MainCard } from '@/components/design-systems/main-card'
import { SimpleBar } from '@/components/third-party/simplebar'
import { ThemeDirection, ThemeMode } from '@/config'
import type { AuthRole } from '@/hooks/api/user'
import { useCurrentUser } from '@/hooks/use-current-user'
import { useMenuCollapse } from '@/hooks/use-menu-collapse'
import type { NavItemType } from '@/menu/types'
import { NavItem } from './nav-item'

interface NavCollapseProps {
  item: NavItemType
  level?: number
}

const popperArrowStyles = (theme: Theme) => ({
  content: '""',
  display: 'block',
  position: 'absolute',
  top: 20,
  left: -6,
  width: 10,
  height: 10,
  bgcolor: 'background.paper',
  transform: 'translateY(-50%) rotate(45deg)',
  zIndex: 120,
  boxShadow: theme.customShadows.tooltip,
  ...(theme.direction !== ThemeDirection.RTL && {
    borderLeft: '1px solid',
    borderLeftColor: 'divider',
    borderBottom: '1px solid',
    borderBottomColor: 'divider'
  }),
  ...(theme.direction === ThemeDirection.RTL && {
    borderRight: '1px solid',
    borderRightColor: 'divider',
    transform: 'translateY(-50%) rotate(45deg) scaleX(-1)',
    borderTop: '1px solid',
    borderTopColor: 'divider'
  })
})

// 菜单递归渲染
const NavCollapseLoop = ({
  item,
  authRole
}: {
  item: Extract<NavItemType, { type: 'group' }>
  authRole?: AuthRole
}) =>
  item.children?.map(child => {
    if (child.roles?.length && authRole && !child.roles.includes(authRole)) {
      return null
    }
    return child.type === 'group' ? (
      <NavCollapse key={child.id} item={child} level={1} />
    ) : (
      <Box key={child.id} sx={{ px: 0.75 }}>
        <NavItem item={child} level={1} />
      </Box>
    )
  })

export const NavCollapse = ({ item, level = 0 }: NavCollapseProps) => {
  const theme = useTheme()
  const { user } = useCurrentUser()
  const location = useLocation()
  const { pathname } = location

  const [open, setOpen] = React.useState(false)
  const [selected, setSelected] = React.useState<string | null>(null)
  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null)

  const miniMenuOpened = Boolean(anchorEl)
  const authRole = user.role

  useMenuCollapse(item, pathname, true, setSelected, setOpen, setAnchorEl)

  const isSelected = (level > 0 && open) || miniMenuOpened || selected === item.id
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (level > 0) setOpen(!open)
    setAnchorEl(null)
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    if (!miniMenuOpened && 'url' in item === false) {
      setSelected(null)
    }
    setAnchorEl(null)
  }

  const listItemAvatarStyle = {
    'my': 0.5,
    '&:hover, &:focus': {
      'bgcolor': 'transparent',
      '& .MuiListItemAvatar-root': { bgcolor: 'action.hover' }
    },
    '&.Mui-selected': {
      'bgcolor': 'transparent',
      '& .MuiListItemAvatar-root': {
        bgcolor: 'primary.lighter',
        ...theme.applyStyles('dark', { bgcolor: 'primary.main' })
      },
      '&:hover, &:focus': {
        'bgcolor': 'transparent',
        '& .MuiListItemAvatar-root': { bgcolor: 'primary.light' }
      }
    }
  }

  const listItemStyle = {
    'color': 'text.primary',
    '&:hover, &:focus': {
      'bgcolor': 'transparent',
      '& .MuiListItem-root': { bgcolor: 'action.hover' }
    },
    '&.Mui-selected': {
      'color': 'text.primary',
      'bgcolor': 'transparent',
      '& .MuiListItem-root': {
        bgcolor: 'primary.lighter',
        ...theme.applyStyles('dark', {
          bgcolor: 'primary.main',
          color: 'background.default'
        })
      },
      '&:hover, &:focus': {
        'bgcolor': 'transparent',
        '& .MuiListItem-root': { bgcolor: 'primary.light' }
      }
    }
  }

  const iconColor =
    isSelected && theme.palette.mode === ThemeMode.DARK
      ? theme.palette.background.default
      : theme.palette.text.primary

  return (
    <ListItemButton
      id={`${item.id}-btn`}
      selected={isSelected}
      disableRipple
      sx={{
        p: 0,
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'default',
        ...(level === 0 ? listItemAvatarStyle : listItemStyle)
      }}
      onClick={handleClick}
      onMouseEnter={handleClick}
      onMouseLeave={handleClose}
    >
      {level === 0 ? (
        <ButtonBase tabIndex={-1} sx={{ borderRadius: 2 }} aria-label='list-button'>
          <ListItemAvatar
            sx={{
              minWidth: 32,
              width: 44,
              height: 44,
              borderRadius: 2,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <ListItemIcon sx={{ minWidth: 0 }}>
              <DynamicIcon name={item.icon} size={22} stroke={1.5} color={iconColor} />
            </ListItemIcon>
          </ListItemAvatar>
        </ButtonBase>
      ) : (
        <ListItem tabIndex={-1} sx={{ borderRadius: 1, px: 1, width: 'calc(100% - 12px)' }}>
          <ListItemText primary={item.title} />
          <IconChevronRight size={18} stroke={1.5} />
        </ListItem>
      )}

      <Popper
        open={miniMenuOpened}
        anchorEl={anchorEl}
        placement='right-start'
        sx={{
          'zIndex': 1202,
          'minWidth': 220,
          '& > .MuiPaper-root': {
            'position': 'relative',
            '&:before': { ...popperArrowStyles(theme) }
          },
          '&[data-popper-placement="right-end"]': {
            '& > .MuiPaper-root': {
              'marginBottom': level > 0 ? -3 : -1.5,
              '&:before': { top: 'unset', bottom: 14 }
            }
          }
        }}
        popperOptions={{
          modifiers: [{ name: 'offset', options: { offset: [level > 0 ? -16 : -10, 0] } }]
        }}
      >
        {({ TransitionProps }) => (
          <Grow
            in={miniMenuOpened}
            {...TransitionProps}
            timeout={{ appear: 0, enter: 150, exit: 150 }}
          >
            <MainCard
              sx={{
                p: 0,
                mt: 1.5,
                py: 0.75,
                boxShadow: theme.customShadows.tooltip,
                backgroundImage: 'none',
                transformOrigin: '0 0 0',
                overflow: 'visible',
                borderRadius: 2
              }}
            >
              <ClickAwayListener onClickAway={handleClose}>
                <Box>
                  <SimpleBar
                    sx={{
                      overflowX: 'hidden',
                      overflowY: 'auto',
                      maxHeight: '50vh'
                    }}
                  >
                    {'children' in item && <NavCollapseLoop item={item} authRole={authRole} />}
                  </SimpleBar>
                </Box>
              </ClickAwayListener>
            </MainCard>
          </Grow>
        )}
      </Popper>
    </ListItemButton>
  )
}

import List from '@mui/material/List'
import Typography from '@mui/material/Typography'

import { useCurrentUser } from '@/hooks/use-current-user'
import type { MenuGroup } from '@/menu/types'
import { NavCollapse } from './nav-collapse'
import { NavItem } from './nav-item'

interface NavGroupProps {
  item: MenuGroup
}

const groupDivider = {
  '&:before': {
    content: '""',
    display: 'block',
    position: 'absolute',
    top: 0,
    left: 16,
    height: '1px',
    width: 44,
    bgcolor: 'divider'
  }
}

export const NavGroup = ({ item }: NavGroupProps) => {
  const { user } = useCurrentUser()
  const authRole = user.role

  return (
    <List component='div' sx={{ '&:not(:first-of-type)': groupDivider }}>
      {item.children.map((menuItem, i) => {
        // 权限过滤
        if (menuItem.roles?.length && authRole && !menuItem.roles.includes(authRole)) {
          return null
        }

        switch (menuItem.type) {
          case 'group':
            return <NavCollapse key={menuItem.id} item={menuItem} />
          case 'item':
            return <NavItem key={menuItem.id} item={menuItem} />
          default:
            return (
              <Typography key={i} variant='h6' color='error' align='center'>
                Error: Unknown menu item type
              </Typography>
            )
        }
      })}
    </List>
  )
}

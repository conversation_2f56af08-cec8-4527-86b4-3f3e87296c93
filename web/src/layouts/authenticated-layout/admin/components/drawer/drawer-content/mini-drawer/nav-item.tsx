import * as React from 'react'
import ButtonBase from '@mui/material/ButtonBase'
import ListItemAvatar from '@mui/material/ListItemAvatar'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { useTheme } from '@mui/material/styles'
import { useNavigate, useRouterState } from '@tanstack/react-router'

import { DynamicIcon } from '@/components/design-systems/dynamic-icon'
import { ThemeMode } from '@/config'
import type { MenuItem } from '@/menu/types'
import { useMenuStore } from '@/stores/menu'

interface NavItemProps {
  item: MenuItem
  level?: number
}

export const NavItem = ({ item, level = 0 }: NavItemProps) => {
  const theme = useTheme()
  const pathname = useRouterState().location.pathname
  const navigate = useNavigate()

  const openedItem = useMenuStore(state => state.openedItem)
  const setOpenedItem = useMenuStore.getState().setOpenedItem

  const isSelected = openedItem === item.id

  React.useEffect(() => {
    if (pathname === item.url) {
      setOpenedItem(item.id)
    }
  }, [pathname, item.id, setOpenedItem])

  const iconColor =
    isSelected && theme.palette.mode === ThemeMode.DARK
      ? theme.palette.background.default
      : theme.palette.text.primary

  const listItemAvatarStyle = {
    'p': 0,
    'my': 0.5,
    'alignItems': 'center',
    'justifyContent': 'center',
    'cursor': 'default',
    '&:hover, &:focus': {
      'bgcolor': 'transparent',
      '& .MuiListItemAvatar-root': { bgcolor: 'action.hover' }
    },
    '&.Mui-selected': {
      'bgcolor': 'transparent',
      '& .MuiListItemAvatar-root': {
        bgcolor: 'primary.lighter',
        ...theme.applyStyles('dark', { bgcolor: 'primary.main' })
      },
      '&:hover, &:focus': {
        'bgcolor': 'transparent',
        '& .MuiListItemAvatar-root': { bgcolor: 'primary.light' }
      }
    }
  }

  const listItemTextStyle = {
    'color': 'text.primary',
    '&.Mui-selected': {
      'color': 'primary.main',
      'bgcolor': 'transparent',
      ...theme.applyStyles('dark', { color: 'primary.light' }),
      '&:hover': { bgcolor: 'action.hover' },
      '&.Mui-focusVisible': { bgcolor: 'action.focus' },
      '& .MuiTypography-root': { fontWeight: 600 }
    }
  }

  const handleNavClick = () => {
    if (item.target) {
      window.open(item.url, item.target)
    } else {
      navigate({ to: item.url })
    }
  }

  return (
    <ListItemButton
      id={`${item.id}-btn`}
      selected={isSelected}
      disabled={item.disabled}
      disableRipple={level === 0}
      {...(level > 0 && { href: item.url, ...(item?.target && { target: '_blank' }) })}
      sx={level === 0 ? listItemAvatarStyle : listItemTextStyle}
    >
      {level === 0 && (
        <ButtonBase
          onClick={handleNavClick}
          tabIndex={-1}
          sx={{ borderRadius: 2 }}
          aria-label='list-button'
        >
          <ListItemAvatar
            sx={{
              minWidth: 32,
              width: 44,
              height: 44,
              borderRadius: 2,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <ListItemIcon sx={{ minWidth: 0 }}>
              <DynamicIcon name={item.icon} size={22} stroke={1.5} color={iconColor} />
            </ListItemIcon>
          </ListItemAvatar>
        </ButtonBase>
      )}
      {level > 0 && <ListItemText primary={item.title} />}
    </ListItemButton>
  )
}

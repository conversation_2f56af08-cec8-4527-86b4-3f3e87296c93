import * as React from 'react'
import { useState } from 'react'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Grow from '@mui/material/Grow'
import IconButton from '@mui/material/IconButton'
import ListItemAvatar from '@mui/material/ListItemAvatar'
import ListItemIcon from '@mui/material/ListItemIcon'
import Popper from '@mui/material/Popper'
import Stack from '@mui/material/Stack'
import { type Theme, useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import { IconBolt } from '@tabler/icons-react'
import { Link } from '@tanstack/react-router'

import { MainCard } from '@/components/design-systems/main-card'
import { IconLogo } from '@/components/icons'
import { SimpleBar } from '@/components/third-party/simplebar'
import { DRAWER_WIDTH, ThemeDirection } from '@/config'

const data = {
  title: 'Upgrade Your Experience',
  description:
    'Take your experience to the next level with our premium offering. Buy now and enjoy more!',
  icon: <IconBolt size={16} />
}

const popperArrowStyles = (theme: Theme) => ({
  content: '""',
  display: 'block',
  position: 'absolute',
  bottom: 20,
  left: -6,
  width: 10,
  height: 10,
  bgcolor: 'grey.50',
  transform: 'translateY(-50%) rotate(45deg)',
  zIndex: 120,
  ...(theme.direction !== ThemeDirection.RTL && {
    borderLeft: '1px solid',
    borderLeftColor: 'divider',
    borderBottom: '1px solid',
    borderBottomColor: 'divider'
  }),
  ...(theme.direction === ThemeDirection.RTL && {
    borderRight: '1px solid',
    borderRightColor: 'divider',
    transform: 'translateY(-50%) rotate(45deg) scaleX(-1)',
    borderTop: '1px solid',
    borderTopColor: 'divider'
  }),
  boxShadow: theme.customShadows.tooltip
})

const CardContent = ({
  title,
  description,
  icon
}: {
  title: string
  description: string
  icon: React.ReactNode
}) => (
  <Stack sx={{ gap: 3 }}>
    <Stack direction='row' sx={{ gap: 0.25, alignItems: 'center' }}>
      <IconLogo className='w-24' />
    </Stack>
    <Stack sx={{ gap: 1, alignItems: 'flex-start', textWrap: 'wrap' }}>
      <Typography variant='subtitle1'>{title}</Typography>
      <Typography variant='caption' color='text.secondary'>
        {description}
      </Typography>
      <Button
        startIcon={icon}
        variant='contained'
        component={Link}
        to='#upgrade'
        target='_blank'
        sx={{ mt: 0.5 }}
      >
        升级
      </Button>
    </Stack>
  </Stack>
)

export const NavCard = ({ isMiniDrawer }: { isMiniDrawer: boolean }) => {
  const theme = useTheme()

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [miniMenuOpened, setMiniMenuOpened] = useState(false)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
    setMiniMenuOpened(prev => !prev)
  }

  const handleClose = () => {
    setMiniMenuOpened(false)
  }

  return (
    <>
      {!isMiniDrawer ? (
        <MainCard sx={{ p: 1.5, bgcolor: 'grey.50', boxShadow: 'none', mb: 3 }}>
          <CardContent {...data} />
        </MainCard>
      ) : (
        <IconButton
          sx={{ marginX: 'auto', mb: 3 }}
          onMouseEnter={handleClick}
          onMouseLeave={handleClose}
          aria-label='upgrade plan'
        >
          <ListItemAvatar
            sx={{
              'minWidth': 32,
              'width': 42,
              'height': 42,
              'borderRadius': 2,
              'cursor': 'pointer',
              'display': 'flex',
              'alignItems': 'center',
              'justifyContent': 'center',
              'bgcolor': 'primary.main',
              '&:hover': { bgcolor: 'primary.dark' }
            }}
          >
            <ListItemIcon sx={{ minWidth: 0 }}>
              <IconBolt size={20} color='background' />
            </ListItemIcon>
          </ListItemAvatar>
          <Popper
            open={miniMenuOpened}
            anchorEl={anchorEl}
            placement='right-end'
            sx={{
              'zIndex': 1202,
              'minWidth': 220,
              'maxWidth': `${DRAWER_WIDTH - 24}px`,
              '& > .MuiPaper-root': {
                'position': 'relative',
                'mb': -0.75,
                '&:before': { ...popperArrowStyles(theme) }
              }
            }}
          >
            {({ TransitionProps }) => (
              <Grow
                in={miniMenuOpened}
                {...TransitionProps}
                timeout={{ appear: 0, enter: 150, exit: 150 }}
              >
                <MainCard
                  sx={{
                    p: 1.5,
                    bgcolor: 'grey.50',
                    boxShadow: theme.customShadows.tooltip,
                    backgroundImage: 'none',
                    transformOrigin: '0 0 0',
                    left: 16,
                    overflow: 'visible'
                  }}
                >
                  <ClickAwayListener onClickAway={handleClose}>
                    <Box>
                      <SimpleBar
                        style={{ maxHeight: '50vh', overflowX: 'hidden', overflowY: 'auto' }}
                      >
                        <CardContent {...data} />
                      </SimpleBar>
                    </Box>
                  </ClickAwayListener>
                </MainCard>
              </Grow>
            )}
          </Popper>
        </IconButton>
      )}
    </>
  )
}

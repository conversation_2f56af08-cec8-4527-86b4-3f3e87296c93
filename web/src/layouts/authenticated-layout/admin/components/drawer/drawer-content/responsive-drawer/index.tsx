import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'

import { menuItems } from '@/menu'
import { NavGroup } from './nav-group'

/***************************  DRAWER CONTENT - RESPONSIVE DRAWER  ***************************/

export const ResponsiveDrawer = () => {
  const navGroups = menuItems.items.map((item, index) => {
    switch (item.type) {
      case 'group':
        return <NavGroup key={index} item={item} />
      default:
        return (
          <Typography key={index} variant='h6' color='error' align='center'>
            Fix - Navigation Group
          </Typography>
        )
    }
  })

  return <Box sx={{ py: 1, transition: 'all 0.3s ease-in-out' }}>{navGroups}</Box>
}

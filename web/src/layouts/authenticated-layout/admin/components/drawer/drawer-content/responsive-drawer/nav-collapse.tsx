import * as React from 'react'
import Collapse from '@mui/material/Collapse'
import List from '@mui/material/List'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { useTheme } from '@mui/material/styles'
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react'
import { useLocation } from '@tanstack/react-router'

import { DynamicIcon } from '@/components/design-systems/dynamic-icon'
import { ThemeMode } from '@/config'
import type { AuthRole } from '@/hooks/api/user'
import { useCurrentUser } from '@/hooks/use-current-user'
import { useMenuCollapse } from '@/hooks/use-menu-collapse'
import type { MenuGroup } from '@/menu/types'
import { NavItem } from './nav-item'

// divider 样式
const verticalDivider = {
  '&:after': {
    content: "''",
    position: 'absolute',
    left: 16,
    top: -2,
    height: 'calc(100% + 2px)',
    width: '1px',
    opacity: 1,
    bgcolor: 'divider'
  }
}

// 渲染子菜单
const renderChildren = (item: MenuGroup, authRole?: AuthRole) =>
  item.children.map(child => {
    if (child.roles?.length && authRole && !child.roles.includes(authRole)) return null

    return child.type === 'group' ? (
      <NavCollapse key={child.id} item={child} level={1} />
    ) : (
      <NavItem key={child.id} item={child} level={1} />
    )
  })

interface NavCollapseProps {
  item: MenuGroup
  level?: number
}

export const NavCollapse = ({ item, level = 0 }: NavCollapseProps) => {
  const theme = useTheme()
  const { user } = useCurrentUser()
  const location = useLocation() // ✅ 替代 usePathname

  const [open, setOpen] = React.useState(false)
  const [selected, setSelected] = React.useState<string | null>(null)

  useMenuCollapse(item, location.pathname, false, setSelected, setOpen)

  const isSelected = open || selected === item.id
  const iconColor =
    isSelected && theme.palette.mode === ThemeMode.DARK
      ? theme.palette.background.default
      : theme.palette.text.primary

  const handleClick = () => setOpen(prev => !prev)

  return (
    <>
      <ListItemButton
        id={`${item.id}-btn`}
        selected={isSelected}
        sx={{
          'my': 0.25,
          'color': 'text.primary',
          '&.Mui-selected': {
            'color': 'text.primary',
            ...theme.applyStyles('dark', { color: 'background.default' }),
            '&.Mui-focusVisible': { bgcolor: 'primary.light' }
          }
        }}
        onClick={handleClick}
      >
        {level === 0 && (
          <ListItemIcon>
            <DynamicIcon name={item.icon} color={iconColor} size={18} stroke={1.5} />
          </ListItemIcon>
        )}
        <ListItemText primary={item.title} sx={{ mb: '-1px' }} />
        {open ? (
          <IconChevronUp size={18} stroke={1.5} />
        ) : (
          <IconChevronDown size={18} stroke={1.5} />
        )}
      </ListItemButton>

      <Collapse in={open} timeout='auto' unmountOnExit>
        <List component='div' sx={{ p: 0, pl: 3, position: 'relative', ...verticalDivider }}>
          {renderChildren(item, user.role)}
        </List>
      </Collapse>
    </>
  )
}

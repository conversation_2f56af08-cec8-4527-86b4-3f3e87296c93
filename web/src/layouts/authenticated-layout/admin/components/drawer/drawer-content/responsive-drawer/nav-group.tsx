import React from 'react'
import List from '@mui/material/List'
import Typography from '@mui/material/Typography'

import { useCurrentUser } from '@/hooks/use-current-user'
import type { MenuGroup, NavItemType } from '@/menu/types'
import { NavCollapse } from './nav-collapse'
import { NavItem } from './nav-item'

interface NavGroupProps {
  item: MenuGroup
}

export const NavGroup = ({ item }: NavGroupProps) => {
  const { user } = useCurrentUser()
  const userRole = user.role

  const children = item.children || []
  const navItems = React.useMemo(
    () =>
      children.filter(child => {
        if (child.roles?.length && userRole && !child.roles.includes(userRole)) return false
        return true
      }),
    [item, user]
  )

  const renderNavItem = (menuItem: NavItemType) => {
    switch (menuItem.type) {
      case 'group':
        return <NavCollapse key={menuItem.id} item={menuItem} />
      case 'item':
        return <NavItem key={menuItem.id} item={menuItem} />
    }
  }

  return (
    <List
      component='div'
      subheader={
        !!navItems.length && (
          <Typography component='div' variant='caption' sx={{ mb: 0.75, color: 'grey.700' }}>
            {item.title}
          </Typography>
        )
      }
      sx={{
        '&:not(:first-of-type)': {
          pt: 1,
          borderTop: '1px solid',
          borderColor: 'divider'
        }
      }}
    >
      {navItems.map(renderNavItem)}
    </List>
  )
}

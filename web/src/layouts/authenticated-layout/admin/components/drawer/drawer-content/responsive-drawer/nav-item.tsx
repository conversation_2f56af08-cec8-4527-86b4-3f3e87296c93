import * as React from 'react'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { useTheme } from '@mui/material/styles'
import useMediaQuery from '@mui/material/useMediaQuery'
import { Link, useLocation } from '@tanstack/react-router'

import { DynamicIcon } from '@/components/design-systems/dynamic-icon'
import { ThemeMode } from '@/config'
import type { MenuItem } from '@/menu/types'
import { useMenuStore } from '@/stores/menu'

interface NavItemProps {
  item: MenuItem
  level?: number
}

export const NavItem = ({ item, level = 0 }: NavItemProps) => {
  const theme = useTheme()
  const downMD = useMediaQuery(theme.breakpoints.down('md'))

  const location = useLocation()
  const openedItem = useMenuStore(state => state.openedItem)
  const setOpenedItem = useMenuStore.getState().setOpenedItem
  const setDrawerOpen = useMenuStore.getState().setDrawerOpen

  const isSelected = location.pathname === item.url || openedItem === item.id

  React.useEffect(() => {
    if (location.pathname === item.url) {
      setOpenedItem(item.id)
    }
  }, [location.pathname, item.id, setOpenedItem])

  const iconColor =
    isSelected && theme.palette.mode === ThemeMode.DARK
      ? theme.palette.background.default
      : theme.palette.text.primary

  const handleClick = () => {
    if (downMD) setDrawerOpen(false)
  }

  return (
    <ListItemButton
      id={`${item.id}-btn`}
      selected={isSelected}
      disabled={item.disabled}
      onClick={handleClick}
      component={item.target ? 'a' : Link}
      href={item.target ? item.url : undefined}
      to={item.target ? undefined : item.url}
      target={item.target ? '_blank' : undefined}
      sx={{
        color: 'text.primary',
        ...(level === 0 && {
          'my': 0.25,
          '&.Mui-selected.Mui-focusVisible': { bgcolor: 'primary.light' }
        }),
        ...(level > 0 && {
          '&.Mui-selected': {
            'color': 'primary.main',
            'bgcolor': 'transparent',
            ...theme.applyStyles('dark', { color: 'primary.light' }),
            '&:hover': { bgcolor: 'action.hover' },
            '&.Mui-focusVisible': { bgcolor: 'action.focus' },
            '& .MuiTypography-root': { fontWeight: 600 }
          }
        })
      }}
    >
      {level === 0 && (
        <ListItemIcon>
          <DynamicIcon name={item.icon} color={iconColor} size={18} stroke={1.5} />
        </ListItemIcon>
      )}
      <ListItemText primary={item.title} sx={{ mb: '-1px' }} />
    </ListItemButton>
  )
}

import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import { IconLayoutSidebarLeftCollapse, IconLayoutSidebarRightCollapse } from '@tabler/icons-react'

import { IconLogo } from '@/components/icons'
import { useMenuStore } from '@/stores/menu'

interface DrawerHeaderProps {
  open: boolean
}

export const DrawerHeader = ({ open }: DrawerHeaderProps) => {
  const isDrawerOpen = useMenuStore(state => state.isDrawerOpened)
  const toggleDrawer = useMenuStore.getState().setDrawerOpen

  return (
    <Box sx={{ width: 1, px: 2, py: { xs: 2, md: 2.5 } }}>
      <Stack
        direction='row'
        sx={{
          alignItems: 'center',
          justifyContent: open ? 'space-between' : 'center',
          height: 36
        }}
      >
        {open && <IconLogo />}
        <IconButton
          aria-label='open drawer'
          onClick={() => toggleDrawer(!isDrawerOpen)}
          size='small'
          color='secondary'
          variant='outlined'
        >
          {!isDrawerOpen ? (
            <IconLayoutSidebarRightCollapse size={20} />
          ) : (
            <IconLayoutSidebarLeftCollapse size={20} />
          )}
        </IconButton>
      </Stack>
    </Box>
  )
}

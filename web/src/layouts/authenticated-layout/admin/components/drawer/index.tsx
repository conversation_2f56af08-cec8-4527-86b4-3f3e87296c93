import * as React from 'react'
import Box from '@mui/material/Box'
import Divider from '@mui/material/Divider'
import Drawer from '@mui/material/Drawer'
import useMediaQuery from '@mui/material/useMediaQuery'

import { DRAWER_WIDTH } from '@/config'
import { useMenuStore } from '@/stores/menu'
import { DrawerContent } from './drawer-content'
import { DrawerHeader } from './drawer-header'
import { MiniDrawerStyled } from './mini-drawer-styled'

interface MainDrawerProps {
  window?: () => Window
}

export const MainDrawer = ({ window }: MainDrawerProps) => {
  const drawerOpen = useMenuStore(state => state.isDrawerOpened)
  const toggleDrawer = useMenuStore.getState().setDrawerOpen
  const downSM = useMediaQuery(theme => theme.breakpoints.down('sm'))
  const downMD = useMediaQuery(theme => theme.breakpoints.down('md'))

  React.useEffect(() => {
    if (downMD) {
      toggleDrawer(false)
    } else {
      toggleDrawer(true)
    }
  }, [downMD])

  const container = window !== undefined ? () => window().document.body : undefined

  const drawerContent = React.useMemo(() => <DrawerContent open={drawerOpen} />, [drawerOpen])
  const drawerHeader = React.useMemo(() => <DrawerHeader open={drawerOpen} />, [drawerOpen])

  return (
    <Box component='nav' sx={{ flexShrink: { md: 0 }, zIndex: 1200 }} aria-label='mailbox folders'>
      {/* Temporary drawer for small media */}
      <Drawer
        container={container}
        variant='temporary'
        open={drawerOpen && downSM}
        onClose={() => toggleDrawer(!drawerOpen)}
        slotProps={{
          paper: {
            sx: {
              boxSizing: 'border-box',
              width: DRAWER_WIDTH,
              borderRight: '1px solid',
              borderRightColor: 'divider',
              backgroundImage: 'none',
              boxShadow: 'inherit'
            }
          }
        }}
      >
        {drawerHeader}
        <Divider sx={{ mx: 2 }} />
        {drawerContent}
      </Drawer>

      {/* Permanent mini-drawer for large media */}
      {!downSM && (
        <MiniDrawerStyled variant='permanent' open={drawerOpen}>
          {drawerHeader}
          <Divider sx={{ mx: 2 }} />
          {drawerContent}
        </MiniDrawerStyled>
      )}
    </Box>
  )
}

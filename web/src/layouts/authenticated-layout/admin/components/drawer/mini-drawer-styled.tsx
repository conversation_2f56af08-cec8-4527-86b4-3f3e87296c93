import * as React from 'react'
import Drawer from '@mui/material/Drawer'
import { styled, type Theme } from '@mui/material/styles'

import { DRAWER_WIDTH, MINI_DRAWER_WIDTH } from '@/config'

const commonDrawerStyles = (theme: Theme): React.CSSProperties => ({
  borderRight: `1px solid ${theme.palette.grey[300]}`,
  overflowX: 'hidden' as const
})

const openedMixin = (theme: Theme): React.CSSProperties => ({
  ...commonDrawerStyles(theme),
  width: DRAWER_WIDTH,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen
  })
})

const closedMixin = (theme: Theme): React.CSSProperties => ({
  ...commonDrawerStyles(theme),
  width: MINI_DRAWER_WIDTH,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen
  })
})

export const MiniDrawerStyled = styled(Drawer, {
  shouldForwardProp: prop => prop !== 'open'
})<{
  open?: boolean
}>(({ theme, open }) => {
  const shared: React.CSSProperties = {
    width: DRAWER_WIDTH,
    flexShrink: 0,
    whiteSpace: 'nowrap',
    boxSizing: 'border-box'
  }
  return {
    ...shared,
    ...(open
      ? {
          ...openedMixin(theme),
          '& .MuiDrawer-paper': openedMixin(theme)
        }
      : {
          ...closedMixin(theme),
          '& .MuiDrawer-paper': closedMixin(theme)
        })
  }
})

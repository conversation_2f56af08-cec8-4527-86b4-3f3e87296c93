import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'

import { Breadcrumbs } from '@/components/breadcrumbs'
import { Notification } from '@/components/notification'
import { ProfileSection } from '@/components/profile-section'
import { SearchBar } from '@/components/search-bar'

/***************************  HEADER CONTENT  ***************************/

export const HeaderContent = () => {
  return (
    <Stack
      direction='row'
      sx={{
        alignItems: 'center',
        justifyContent: { xs: 'flex-end', sm: 'space-between' },
        gap: 2,
        width: 1
      }}
    >
      <Box sx={{ display: { xs: 'none', sm: 'block' } }}>
        <Breadcrumbs />
      </Box>
      <Stack direction='row' sx={{ alignItems: 'center', gap: { xs: 1, sm: 1.5 } }}>
        <SearchBar />
        <Notification />
        <ProfileSection />
      </Stack>
    </Stack>
  )
}

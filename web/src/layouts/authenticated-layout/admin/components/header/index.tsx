import * as React from 'react'
import AppBar, { type AppBarProps } from '@mui/material/AppBar'
import IconButton from '@mui/material/IconButton'
import { useTheme } from '@mui/material/styles'
import Toolbar from '@mui/material/Toolbar'
import useMediaQuery from '@mui/material/useMediaQuery'
import { IconMenu2 } from '@tabler/icons-react'

import { DRAWER_WIDTH, MINI_DRAWER_WIDTH } from '@/config'
import { useMenuStore } from '@/stores/menu'
import { AppBarStyled } from './app-bar-styled'
import { HeaderContent } from './header-content'

/***************************  ADMIN LAYOUT - HEADER  ***************************/

export const Header = () => {
  const theme = useTheme()
  const downSM = useMediaQuery(theme.breakpoints.down('sm'))
  const { setDrawerOpen, isDrawerOpened } = useMenuStore()

  const headerContent = React.useMemo(() => <HeaderContent />, [])

  // Common header content
  const mainHeader = (
    <Toolbar sx={{ minHeight: { xs: 68, md: 76 } }}>
      <IconButton
        aria-label='open drawer'
        onClick={() => setDrawerOpen(!isDrawerOpened)}
        size='small'
        color='secondary'
        variant='outlined'
        sx={{ display: { xs: 'inline-flex', sm: 'none' }, mr: 1 }}
      >
        <IconMenu2 size={20} />
      </IconButton>
      {headerContent}
    </Toolbar>
  )

  // AppBar props, including styles that vary based on drawer state and screen size
  const appBar: AppBarProps = {
    color: 'inherit',
    position: 'fixed',
    elevation: 0,
    sx: {
      borderBottom: `1px solid ${theme.palette.grey[300]}`,
      zIndex: 1200,
      width: {
        xs: '100%',
        sm: isDrawerOpened
          ? `calc(100% - ${DRAWER_WIDTH}px)`
          : `calc(100% - ${MINI_DRAWER_WIDTH}px)`
      }
    }
  }

  return (
    <>
      {downSM ? (
        <AppBarStyled open={isDrawerOpened} {...appBar}>
          {mainHeader}
        </AppBarStyled>
      ) : (
        <AppBar {...appBar}>{mainHeader}</AppBar>
      )}
    </>
  )
}

import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import Stack from '@mui/material/Stack'
import Toolbar from '@mui/material/Toolbar'
import { Outlet } from '@tanstack/react-router'

import { Breadcrumbs } from '@/components/breadcrumbs'
import { DRAWER_WIDTH } from '@/config'
import { MainDrawer } from './components/drawer'
import { Header } from './components/header'

export const AdminLayout = () => {
  return (
    <Stack direction='row' width={1}>
      <Header />
      <MainDrawer />
      <Box component='main' sx={{ width: `calc(100% - ${DRAWER_WIDTH}px)`, flexGrow: 1 }}>
        <Toolbar sx={{ minHeight: { xs: 68, md: 76 } }} />
        <Box
          sx={{
            py: 0.4,
            px: 1.5,
            display: { xs: 'block', sm: 'none' },
            borderBottom: 1,
            borderColor: 'divider',
            mb: 2
          }}
        >
          <Breadcrumbs />
        </Box>
        <Container maxWidth='lg' sx={{ px: 2, py: { sm: 2 } }}>
          <Outlet />
        </Container>
      </Box>
    </Stack>
  )
}

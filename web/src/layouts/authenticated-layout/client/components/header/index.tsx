import AppBar, { type AppBarProps } from '@mui/material/AppBar'
import { useTheme } from '@mui/material/styles'
import Toolbar from '@mui/material/Toolbar'

import { HeaderContent } from './header-content'

/***************************  CLIENT LAYOUT - HEADER  ***************************/

export const Header = () => {
  const theme = useTheme()

  // AppBar props, including styles that vary based on drawer state and screen size
  const appBar: AppBarProps = {
    color: 'inherit',
    position: 'fixed',
    elevation: 0,
    sx: {
      borderBottom: `1px solid ${theme.palette.grey[300]}`,
      zIndex: 1200,
      width: '100%'
    }
  }

  return (
    <AppBar {...appBar}>
      <Toolbar sx={{ minHeight: { xs: 68, md: 76 } }}>
        <HeaderContent />
      </Toolbar>
    </AppBar>
  )
}

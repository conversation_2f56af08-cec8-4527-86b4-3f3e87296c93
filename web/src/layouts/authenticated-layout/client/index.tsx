import Box from '@mui/material/Box'
import Container from '@mui/material/Container'
import Stack from '@mui/material/Stack'
import Toolbar from '@mui/material/Toolbar'
import { Outlet } from '@tanstack/react-router'

import { Header } from './components/header'

export const ClientLayout = () => {
  return (
    <Stack direction='column' width={1}>
      <Header />

      <Box component='main' sx={{ flexGrow: 1 }}>
        <Toolbar sx={{ minHeight: { xs: 68, md: 76 } }} />
        <Container maxWidth='lg' sx={{ p: 2 }}>
          <Outlet />
        </Container>
      </Box>
    </Stack>
  )
}

import { jwtDecode } from 'jwt-decode'
import { toast } from 'sonner'

import { TokenPayloadSchema } from '@/hooks/api/auth'
import { createLocalStorage } from './storage'

// 认证策略接口
export interface AuthStrategy<T> {
  getToken: () => string | null
  setToken: (token: string | null) => void
  isAuthenticated: () => boolean
  getPayload: () => T | null
}

// JWT 本地存储策略
export type TDecodeTokenFn<T> = (token: string) => T | null
export type TValidateTokenFn = (token: string) => boolean

const decodeToken = <T>(token: string): T | null => {
  try {
    return jwtDecode<T>(token)
  } catch {
    return null
  }
}

const validateToken = (token: string | null): token is string => {
  if (!token) return false
  const payload = decodeToken(token)
  if (!payload) return false
  return TokenPayloadSchema.safeParse(payload).success
}

export const toastTokenInvalid = () => {
  toast.error(`无效的AccessToken`, {
    description: '请重新登录, 或联系管理员'
  })
}

export const createLocalStorageStrategy = <T>(
  key: string,
  decode: TDecodeTokenFn<T> = decodeToken,
  validate: TValidateTokenFn = validateToken
): AuthStrategy<T> => {
  const storage = createLocalStorage(key)
  return {
    getToken: () => storage.get(),
    setToken: token => storage.set(token),
    isAuthenticated: () => {
      const token = storage.get()
      return !!token && validate(token)
    },
    getPayload: () => {
      const token = storage.get()
      const isValid = !!token && validate(token)
      return isValid ? decode(token) : null
    }
  }
}

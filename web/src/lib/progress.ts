import NProgress from 'nprogress'

import { removeBothEndsSlash } from './url'

let timerId: NodeJS.Timeout | null = null

const ProgressBlacklist: string[] = []

export function start(url?: string) {
  if (!isBlacklisted(url) && !NProgress.isStarted) {
    NProgress.start()
  }
  if (timerId) clearTimeout(timerId)
  timerId = setTimeout(() => {
    NProgress.remove()
  }, 7000)
}

export function done(url?: string) {
  if (!isBlacklisted(url)) {
    NProgress.done(true)
  }
  if (timerId) clearTimeout(timerId)
}

function isBlacklisted(url?: string) {
  if (url) {
    for (const blackUrl of ProgressBlacklist) {
      if (url.indexOf(removeBothEndsSlash(blackUrl)) > -1) {
        return true
      }
    }
  }
  return false
}

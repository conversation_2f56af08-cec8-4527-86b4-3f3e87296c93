export const STORAGE_CHANGE_EVENT = 'custom-storage-change'

interface Storage<T> {
  get: () => T | null
  set: (value: T | null) => void
  remove: () => void
}

// 本地存储实现
export const dispatchStorageChangeEvent = <T>(key: string, value: T | null) => {
  window.dispatchEvent(
    new CustomEvent(STORAGE_CHANGE_EVENT, {
      detail: { key, value }
    })
  )
}

export const setLocalStorage = <T>(key: string, value?: T | null) => {
  if (value === null || value === undefined) {
    removeLocalStorage(key)
    return
  }
  localStorage.setItem(key, JSON.stringify(value))
  dispatchStorageChangeEvent(key, value)
}

export const removeLocalStorage = (key: string) => {
  localStorage.removeItem(key)
  dispatchStorageChangeEvent(key, null)
}

export const getLocalStorage = (key: string): string | null => {
  const value = localStorage.getItem(key)
  const parsedValue = value !== null ? (JSON.parse(value) ? JSON.parse(value) : null) : null
  return parsedValue
}

export const createLocalStorage = (key: string): Storage<string> => ({
  get: () => getLocalStorage(key),
  set: (value: string | null) => setLocalStorage(key, value),
  remove: () => removeLocalStorage(key)
})

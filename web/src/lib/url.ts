/**
 * remove the suffix when URL have slash, otherwise do nothing
 *
 * @category utils/url
 * @param {string} url
 * @example
 * //return: /blog
 * removeURLSuffixSlash('/blog/')
 *
 * //return: /blog
 * removeURLSuffixSlash('/blog')
 */
export const removeURLSuffixSlash = (url: string) => url.replace(/\/+$/, '')

/**
 * remove the suffix when URL have slash, otherwise do nothing
 *
 * @category utils/url
 * @param {string} url
 * @example
 * //return: blog/
 * removeURLPrefixSlash('/blog/')
 *
 * //return: blog
 * removeURLPrefixSlash('/blog')
 */
export const removeURLPrefixSlash = (url: string) => url.replace(/^\/+/, '')

/**
 * remove the both ends slash
 *
 * @category utils/url
 * @param {string} url
 * @example
 * //return: blog
 * removeBothSideSlash('/blog/')
 *
 * //return: blog
 * removeBothSideSlash('/blog')
 */
export const removeBothEndsSlash = (url: string) => removeURLPrefixSlash(removeURLSuffixSlash(url))

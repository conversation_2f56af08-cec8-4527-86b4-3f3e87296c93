import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { broadcastQueryClient } from '@tanstack/query-broadcast-client-experimental'
import { createRouter, RouterProvider } from '@tanstack/react-router'

import { Loader } from './components/design-systems/loader.tsx'
import { AppProviders } from './components/providers/app-providers.tsx'
import { useAuth } from './contexts/auth/context.tsx'
import { queryClient } from './hooks/api/react-query'
// Import the generated route tree
import { routeTree } from './routeTree.gen'

/* simplebar styles */
import 'simplebar-react/dist/simplebar.min.css'
// https://mui.com/material-ui/getting-started/installation/#roboto-font
import '@fontsource/roboto/300.css'
import '@fontsource/roboto/400.css'
import '@fontsource/roboto/500.css'
import '@fontsource/roboto/700.css'
import './index.css'

broadcastQueryClient({
  queryClient,
  broadcastChannel: 'safe-trace:query-client'
})

// Create a new router instance
const router = createRouter({
  routeTree,
  context: {
    queryClient,
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    auth: undefined!
  },
  defaultPendingComponent: Loader
})

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const InnerApp = () => {
  const auth = useAuth()
  return <RouterProvider router={router} context={{ auth }} />
}

const rootElement = document.getElementById('root')
if (!rootElement) {
  throw new Error('Root element not found')
}
createRoot(rootElement).render(
  <StrictMode>
    <AppProviders>
      <InnerApp />
    </AppProviders>
  </StrictMode>
)

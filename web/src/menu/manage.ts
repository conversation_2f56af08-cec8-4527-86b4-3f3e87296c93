import { ROUTE_PATHS } from '@/constants/routes'
import { AuthRole } from '@/hooks/api/user'
import type { MenuGroup } from './types'

export const manage: MenuGroup = {
  id: 'manage',
  type: 'group',
  title: '管理',
  icon: 'IconBrand<PERSON>ana',
  children: [
    {
      id: 'dashboard',
      type: 'item',
      title: '仪表盘',
      url: ROUTE_PATHS.ADMIN_DASHBOARD.path,
      icon: 'IconDashboard',
      roles: [AuthRole.ADMIN]
    },
    {
      id: 'user-management',
      type: 'item',
      title: '用户管理',
      url: ROUTE_PATHS.USER_MANAGEMENT.path,
      icon: 'IconUserCog',
      roles: [AuthRole.ADMIN]
    }
  ]
}

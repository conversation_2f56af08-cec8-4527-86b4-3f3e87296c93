import * as TablerIcons from '@tabler/icons-react'

import type { RoutePath } from '@/constants/routes'
import type { AuthRole } from '@/hooks/api/user'

type IconName = keyof typeof TablerIcons

// 菜单类型
export type MenuType = 'group' | 'item'

// 基础菜单项结构
export interface BaseMenuItem {
  id: string
  title: string
  icon: IconName
  roles?: AuthRole[]
  disabled?: boolean
  target?: '_blank' | '_self'
}

// 子菜单项（普通链接项）
export interface MenuItem extends BaseMenuItem {
  type: 'item'
  url: RoutePath
}

// 菜单分组（有子项）
export interface MenuGroup extends BaseMenuItem {
  type: 'group'
  children: NavItemType[]
}

// 联合类型：菜单项或分组
export type NavItemType = MenuItem | MenuGroup

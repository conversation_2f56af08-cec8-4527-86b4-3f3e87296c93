import Button from '@mui/material/Button'
import Link from '@mui/material/Link'
import Stack from '@mui/material/Stack'
import { Link as TLink, Navigate } from '@tanstack/react-router'

import { IconFeishu } from '@/components/icons'
import { ROUTE_PATHS } from '@/constants/routes'
import { useAuth } from '@/contexts/auth'
import { redirectToFeishuLogin } from '@/lib/oauth'

export const LoginFeishuPage = () => {
  const { isAuthenticated } = useAuth()

  if (isAuthenticated) {
    return (
      <Navigate to={ROUTE_PATHS.INDEX.path} from={ROUTE_PATHS.AUTH_LOGIN_FEISHU.path} replace />
    )
  }

  return (
    <Stack alignItems='center' gap={2}>
      <Button
        variant='outlined'
        size='large'
        sx={{
          'width': 100,
          'height': 100,
          'p': 2,
          '.MuiButton-startIcon': { m: 0 }
        }}
        startIcon={<IconFeishu className='w-full' />}
        onClick={redirectToFeishuLogin}
      />

      <Link
        component={TLink}
        underline='hover'
        variant='body2'
        href='/auth/login-password'
        sx={{ '&:hover': { color: 'primary.dark' } }}
      >
        用户名登录
      </Link>
    </Stack>
  )
}

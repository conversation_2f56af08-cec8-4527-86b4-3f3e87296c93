import Button from '@mui/material/Button'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { Navigate } from '@tanstack/react-router'

import { AuthLoginPasswordForm } from '@/components/auth-login-password-form'
import { IconFeishu } from '@/components/icons'
import { ROUTE_PATHS } from '@/constants/routes'
import { useAuth } from '@/contexts/auth'
import { redirectToFeishuLogin } from '@/lib/oauth'

export const AuthLoginPasswordPage = () => {
  const { isAuthenticated } = useAuth()

  if (isAuthenticated) {
    return <Navigate to={ROUTE_PATHS.INDEX.path} replace />
  }

  return (
    <Stack>
      <Button
        variant='outlined'
        fullWidth
        size='small'
        color='secondary'
        sx={{
          marginBottom: 2
        }}
        startIcon={<IconFeishu />}
        onClick={redirectToFeishuLogin}
      >
        <Typography variant='caption1'>通过飞书登录</Typography>
      </Button>

      <AuthLoginPasswordForm />
    </Stack>
  )
}

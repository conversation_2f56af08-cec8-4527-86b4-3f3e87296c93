import { createFileRoute } from '@tanstack/react-router'

import { useCurrentUser } from '@/hooks/use-current-user'
import { AdminLayout } from '@/layouts/authenticated-layout/admin'
import { ClientLayout } from '@/layouts/authenticated-layout/client'

const Layout = () => {
  const { user } = useCurrentUser()
  if (user.role === 'admin') {
    return <AdminLayout />
  }
  return <ClientLayout />
}

export const Route = createFileRoute('/_auth/_role/_authenticated-layout')({
  component: Layout
})

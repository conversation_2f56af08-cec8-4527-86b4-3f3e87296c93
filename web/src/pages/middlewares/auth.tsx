import { createFileRoute, Navigate, Outlet } from '@tanstack/react-router'

import { ROUTE_PATHS } from '@/constants/routes'
import { useAuth } from '@/contexts/auth'

const AuthMiddleware = () => {
  const { isAuthenticated } = useAuth()

  if (!isAuthenticated) {
    return <Navigate to={ROUTE_PATHS.AUTH_LOGIN_FEISHU.path} />
  }
  return <Outlet />
}

export const Route = createFileRoute('/_auth')({
  component: AuthMiddleware
})

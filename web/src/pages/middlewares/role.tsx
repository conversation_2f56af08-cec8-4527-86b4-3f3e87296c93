import * as React from 'react'
import Container from '@mui/material/Container'
import Typography from '@mui/material/Typography'
import { createFileRoute, Navigate, Outlet, useLocation } from '@tanstack/react-router'
import { toast } from 'sonner'

import { PageLoader } from '@/components/design-systems/page-loader'
import { ROUTE_PATHS } from '@/constants/routes'
import { AuthRole } from '@/hooks/api/user'
import { useCurrentUser } from '@/hooks/use-current-user'
import { menuItems } from '@/menu'
import type { MenuItem, NavItemType } from '@/menu/types'

const hasAccess = (item: MenuItem | null, role: AuthRole): boolean =>
  !item?.roles?.length || item.roles.includes(role)

const findCurrentMenuItem = (pathname: string): MenuItem | null => {
  for (const menu of menuItems.items) {
    if (menu.type === 'group') {
      const matched = findMenuPathChain(menu.children, pathname)
      if (matched) return matched[0] as MenuItem
    }
  }
  return null
}

const findMenuPathChain = (
  navItems: NavItemType[],
  targetUrl: string,
  parents: NavItemType[] = []
): NavItemType[] | null => {
  for (const item of navItems) {
    const newParents = [...parents, item]

    if (item.type === 'item' && item.url === targetUrl) {
      return newParents
    }

    if (item.type === 'group' && item.children.length > 0) {
      const result = findMenuPathChain(item.children, targetUrl, newParents)
      if (result) {
        return result
      }
    }
  }

  return null
}

export const RoleMiddleware = () => {
  const { user, isPending } = useCurrentUser()
  const { pathname } = useLocation()
  const [activeItem, setActiveItem] = React.useState<MenuItem | null>(null)

  React.useEffect(() => {
    const matchedItem = findCurrentMenuItem(pathname)
    setActiveItem(matchedItem)
  }, [pathname])

  if (isPending) {
    return <PageLoader />
  }

  if (!user) {
    toast.error('获取用户信息失败，请重新登录！')
    return <Navigate to={ROUTE_PATHS.AUTH_LOGIN_FEISHU.path} />
  }

  // 必要的权限检查
  if (user?.role !== AuthRole.ADMIN && user?.role !== AuthRole.USER) {
    toast.error('您没有任何权限，请联系管理员！')
    return <Navigate to={ROUTE_PATHS.AUTH_LOGIN_FEISHU.path} />
  }

  // 检查用户是否有访问当前路由的权限
  if (!hasAccess(activeItem, user.role)) {
    return (
      <Container sx={{ textAlign: 'center' }}>
        <Typography variant='h3' sx={{ mb: 2 }}>
          Permission Denied
        </Typography>
        <Typography sx={{ color: 'text.secondary' }}>
          You do not have permission to access this page.
        </Typography>
      </Container>
    )
  }

  return <Outlet />
}

export const Route = createFileRoute('/_auth/_role')({
  component: RoleMiddleware
})

import type { QueryClient } from '@tanstack/react-query'
import { createRootRouteWithContext, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'

import type { AuthContextType } from '@/contexts/auth'

interface TRouterContext {
  queryClient: QueryClient
  auth: AuthContextType
}

const RootPage = () => {
  return (
    <>
      <Outlet />
      <TanStackRouterDevtools />
    </>
  )
}

export const Route = createRootRouteWithContext<TRouterContext>()({
  component: RootPage
})

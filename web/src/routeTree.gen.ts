/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './pages/root'
import { Route as middlewaresAuthImport } from './pages/middlewares/auth'
import { Route as authLayoutImport } from './pages/auth/layout'
import { Route as middlewaresRoleImport } from './pages/middlewares/role'
import { Route as authenticatedLayoutImport } from './pages/authenticated/layout'
import { Route as authenticatedadminLayoutImport } from './pages/authenticated/(admin)/layout'
import { Route as authLoginPasswordRouteImport } from './pages/auth/login-password/route'
import { Route as authLoginFeishuRouteImport } from './pages/auth/login-feishu/route'
import { Route as authenticatedIndexImport } from './pages/authenticated/index'
import { Route as authenticatedProjectsImport } from './pages/authenticated/projects'
import { Route as authenticatedadminUserManagementImport } from './pages/authenticated/(admin)/user-management'
import { Route as authenticatedadminDashboardImport } from './pages/authenticated/(admin)/dashboard'

// Create Virtual Routes

const AuthImport = createFileRoute('/auth')()

// Create/Update Routes

const AuthRoute = AuthImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRoute,
} as any)

const middlewaresAuthRoute = middlewaresAuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const authLayoutRoute = authLayoutImport.update({
  id: '/_auth-layout',
  getParentRoute: () => AuthRoute,
} as any)

const middlewaresRoleRoute = middlewaresRoleImport.update({
  id: '/_role',
  getParentRoute: () => middlewaresAuthRoute,
} as any)

const authenticatedLayoutRoute = authenticatedLayoutImport.update({
  id: '/_authenticated-layout',
  getParentRoute: () => middlewaresRoleRoute,
} as any)

const authenticatedadminLayoutRoute = authenticatedadminLayoutImport.update({
  id: '/_admin-layout',
  getParentRoute: () => middlewaresRoleRoute,
} as any)

const authLoginPasswordRouteRoute = authLoginPasswordRouteImport.update({
  id: '/login-password',
  path: '/login-password',
  getParentRoute: () => authLayoutRoute,
} as any)

const authLoginFeishuRouteRoute = authLoginFeishuRouteImport.update({
  id: '/login-feishu',
  path: '/login-feishu',
  getParentRoute: () => authLayoutRoute,
} as any)

const authenticatedIndexRoute = authenticatedIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => authenticatedLayoutRoute,
} as any)

const authenticatedProjectsRoute = authenticatedProjectsImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => authenticatedLayoutRoute,
} as any)

const authenticatedadminUserManagementRoute =
  authenticatedadminUserManagementImport.update({
    id: '/users',
    path: '/users',
    getParentRoute: () => authenticatedadminLayoutRoute,
  } as any)

const authenticatedadminDashboardRoute =
  authenticatedadminDashboardImport.update({
    id: '/dashboard',
    path: '/dashboard',
    getParentRoute: () => authenticatedadminLayoutRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof middlewaresAuthImport
      parentRoute: typeof rootRoute
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/_auth/_role': {
      id: '/_auth/_role'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof middlewaresRoleImport
      parentRoute: typeof middlewaresAuthImport
    }
    '/auth/_auth-layout': {
      id: '/auth/_auth-layout'
      path: ''
      fullPath: '/auth'
      preLoaderRoute: typeof authLayoutImport
      parentRoute: typeof AuthImport
    }
    '/auth/_auth-layout/login-feishu': {
      id: '/auth/_auth-layout/login-feishu'
      path: '/login-feishu'
      fullPath: '/auth/login-feishu'
      preLoaderRoute: typeof authLoginFeishuRouteImport
      parentRoute: typeof authLayoutImport
    }
    '/auth/_auth-layout/login-password': {
      id: '/auth/_auth-layout/login-password'
      path: '/login-password'
      fullPath: '/auth/login-password'
      preLoaderRoute: typeof authLoginPasswordRouteImport
      parentRoute: typeof authLayoutImport
    }
    '/_auth/_role/_admin-layout': {
      id: '/_auth/_role/_admin-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof authenticatedadminLayoutImport
      parentRoute: typeof middlewaresRoleImport
    }
    '/_auth/_role/_authenticated-layout': {
      id: '/_auth/_role/_authenticated-layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof authenticatedLayoutImport
      parentRoute: typeof middlewaresRoleImport
    }
    '/_auth/_role/_admin-layout/dashboard': {
      id: '/_auth/_role/_admin-layout/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof authenticatedadminDashboardImport
      parentRoute: typeof authenticatedadminLayoutImport
    }
    '/_auth/_role/_admin-layout/users': {
      id: '/_auth/_role/_admin-layout/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof authenticatedadminUserManagementImport
      parentRoute: typeof authenticatedadminLayoutImport
    }
    '/_auth/_role/_authenticated-layout/projects': {
      id: '/_auth/_role/_authenticated-layout/projects'
      path: '/projects'
      fullPath: '/projects'
      preLoaderRoute: typeof authenticatedProjectsImport
      parentRoute: typeof authenticatedLayoutImport
    }
    '/_auth/_role/_authenticated-layout/': {
      id: '/_auth/_role/_authenticated-layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof authenticatedIndexImport
      parentRoute: typeof authenticatedLayoutImport
    }
  }
}

// Create and export the route tree

interface authenticatedadminLayoutRouteChildren {
  authenticatedadminDashboardRoute: typeof authenticatedadminDashboardRoute
  authenticatedadminUserManagementRoute: typeof authenticatedadminUserManagementRoute
}

const authenticatedadminLayoutRouteChildren: authenticatedadminLayoutRouteChildren =
  {
    authenticatedadminDashboardRoute: authenticatedadminDashboardRoute,
    authenticatedadminUserManagementRoute:
      authenticatedadminUserManagementRoute,
  }

const authenticatedadminLayoutRouteWithChildren =
  authenticatedadminLayoutRoute._addFileChildren(
    authenticatedadminLayoutRouteChildren,
  )

interface authenticatedLayoutRouteChildren {
  authenticatedProjectsRoute: typeof authenticatedProjectsRoute
  authenticatedIndexRoute: typeof authenticatedIndexRoute
}

const authenticatedLayoutRouteChildren: authenticatedLayoutRouteChildren = {
  authenticatedProjectsRoute: authenticatedProjectsRoute,
  authenticatedIndexRoute: authenticatedIndexRoute,
}

const authenticatedLayoutRouteWithChildren =
  authenticatedLayoutRoute._addFileChildren(authenticatedLayoutRouteChildren)

interface middlewaresRoleRouteChildren {
  authenticatedadminLayoutRoute: typeof authenticatedadminLayoutRouteWithChildren
  authenticatedLayoutRoute: typeof authenticatedLayoutRouteWithChildren
}

const middlewaresRoleRouteChildren: middlewaresRoleRouteChildren = {
  authenticatedadminLayoutRoute: authenticatedadminLayoutRouteWithChildren,
  authenticatedLayoutRoute: authenticatedLayoutRouteWithChildren,
}

const middlewaresRoleRouteWithChildren = middlewaresRoleRoute._addFileChildren(
  middlewaresRoleRouteChildren,
)

interface middlewaresAuthRouteChildren {
  middlewaresRoleRoute: typeof middlewaresRoleRouteWithChildren
}

const middlewaresAuthRouteChildren: middlewaresAuthRouteChildren = {
  middlewaresRoleRoute: middlewaresRoleRouteWithChildren,
}

const middlewaresAuthRouteWithChildren = middlewaresAuthRoute._addFileChildren(
  middlewaresAuthRouteChildren,
)

interface authLayoutRouteChildren {
  authLoginFeishuRouteRoute: typeof authLoginFeishuRouteRoute
  authLoginPasswordRouteRoute: typeof authLoginPasswordRouteRoute
}

const authLayoutRouteChildren: authLayoutRouteChildren = {
  authLoginFeishuRouteRoute: authLoginFeishuRouteRoute,
  authLoginPasswordRouteRoute: authLoginPasswordRouteRoute,
}

const authLayoutRouteWithChildren = authLayoutRoute._addFileChildren(
  authLayoutRouteChildren,
)

interface AuthRouteChildren {
  authLayoutRoute: typeof authLayoutRouteWithChildren
}

const AuthRouteChildren: AuthRouteChildren = {
  authLayoutRoute: authLayoutRouteWithChildren,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof authenticatedLayoutRouteWithChildren
  '/auth': typeof authLayoutRouteWithChildren
  '/auth/login-feishu': typeof authLoginFeishuRouteRoute
  '/auth/login-password': typeof authLoginPasswordRouteRoute
  '/dashboard': typeof authenticatedadminDashboardRoute
  '/users': typeof authenticatedadminUserManagementRoute
  '/projects': typeof authenticatedProjectsRoute
  '/': typeof authenticatedIndexRoute
}

export interface FileRoutesByTo {
  '': typeof authenticatedadminLayoutRouteWithChildren
  '/auth': typeof authLayoutRouteWithChildren
  '/auth/login-feishu': typeof authLoginFeishuRouteRoute
  '/auth/login-password': typeof authLoginPasswordRouteRoute
  '/dashboard': typeof authenticatedadminDashboardRoute
  '/users': typeof authenticatedadminUserManagementRoute
  '/projects': typeof authenticatedProjectsRoute
  '/': typeof authenticatedIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof middlewaresAuthRouteWithChildren
  '/auth': typeof AuthRouteWithChildren
  '/_auth/_role': typeof middlewaresRoleRouteWithChildren
  '/auth/_auth-layout': typeof authLayoutRouteWithChildren
  '/auth/_auth-layout/login-feishu': typeof authLoginFeishuRouteRoute
  '/auth/_auth-layout/login-password': typeof authLoginPasswordRouteRoute
  '/_auth/_role/_admin-layout': typeof authenticatedadminLayoutRouteWithChildren
  '/_auth/_role/_authenticated-layout': typeof authenticatedLayoutRouteWithChildren
  '/_auth/_role/_admin-layout/dashboard': typeof authenticatedadminDashboardRoute
  '/_auth/_role/_admin-layout/users': typeof authenticatedadminUserManagementRoute
  '/_auth/_role/_authenticated-layout/projects': typeof authenticatedProjectsRoute
  '/_auth/_role/_authenticated-layout/': typeof authenticatedIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/auth'
    | '/auth/login-feishu'
    | '/auth/login-password'
    | '/dashboard'
    | '/users'
    | '/projects'
    | '/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | ''
    | '/auth'
    | '/auth/login-feishu'
    | '/auth/login-password'
    | '/dashboard'
    | '/users'
    | '/projects'
    | '/'
  id:
    | '__root__'
    | '/_auth'
    | '/auth'
    | '/_auth/_role'
    | '/auth/_auth-layout'
    | '/auth/_auth-layout/login-feishu'
    | '/auth/_auth-layout/login-password'
    | '/_auth/_role/_admin-layout'
    | '/_auth/_role/_authenticated-layout'
    | '/_auth/_role/_admin-layout/dashboard'
    | '/_auth/_role/_admin-layout/users'
    | '/_auth/_role/_authenticated-layout/projects'
    | '/_auth/_role/_authenticated-layout/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  middlewaresAuthRoute: typeof middlewaresAuthRouteWithChildren
  AuthRoute: typeof AuthRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  middlewaresAuthRoute: middlewaresAuthRouteWithChildren,
  AuthRoute: AuthRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "root.tsx",
      "children": [
        "/_auth",
        "/auth"
      ]
    },
    "/_auth": {
      "filePath": "middlewares/auth.tsx",
      "children": [
        "/_auth/_role"
      ]
    },
    "/auth": {
      "filePath": "",
      "children": [
        "/auth/_auth-layout"
      ]
    },
    "/_auth/_role": {
      "filePath": "middlewares/role.tsx",
      "parent": "/_auth",
      "children": [
        "/_auth/_role/_admin-layout",
        "/_auth/_role/_authenticated-layout"
      ]
    },
    "/auth/_auth-layout": {
      "filePath": "auth/layout.tsx",
      "parent": "/auth",
      "children": [
        "/auth/_auth-layout/login-feishu",
        "/auth/_auth-layout/login-password"
      ]
    },
    "/auth/_auth-layout/login-feishu": {
      "filePath": "auth/login-feishu/route.tsx",
      "parent": "/auth/_auth-layout"
    },
    "/auth/_auth-layout/login-password": {
      "filePath": "auth/login-password/route.tsx",
      "parent": "/auth/_auth-layout"
    },
    "/_auth/_role/_admin-layout": {
      "filePath": "authenticated/(admin)/layout.tsx",
      "parent": "/_auth/_role",
      "children": [
        "/_auth/_role/_admin-layout/dashboard",
        "/_auth/_role/_admin-layout/users"
      ]
    },
    "/_auth/_role/_authenticated-layout": {
      "filePath": "authenticated/layout.tsx",
      "parent": "/_auth/_role",
      "children": [
        "/_auth/_role/_authenticated-layout/projects",
        "/_auth/_role/_authenticated-layout/"
      ]
    },
    "/_auth/_role/_admin-layout/dashboard": {
      "filePath": "authenticated/(admin)/dashboard.tsx",
      "parent": "/_auth/_role/_admin-layout"
    },
    "/_auth/_role/_admin-layout/users": {
      "filePath": "authenticated/(admin)/user-management.tsx",
      "parent": "/_auth/_role/_admin-layout"
    },
    "/_auth/_role/_authenticated-layout/projects": {
      "filePath": "authenticated/projects.tsx",
      "parent": "/_auth/_role/_authenticated-layout"
    },
    "/_auth/_role/_authenticated-layout/": {
      "filePath": "authenticated/index.tsx",
      "parent": "/_auth/_role/_authenticated-layout"
    }
  }
}
ROUTE_MANIFEST_END */

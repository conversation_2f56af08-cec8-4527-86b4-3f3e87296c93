import {
  index,
  layout,
  rootRoute,
  route,
  type VirtualRouteNode
} from '@tanstack/virtual-file-routes'

const middleware = (fileName: string, virtualRoutes: VirtualRouteNode[]) =>
  layout(`middlewares/${fileName}`, virtualRoutes)

// 身份验证与权限验证中间件
const authWithRoleMiddleware = (virtualRoutes: VirtualRouteNode[]) =>
  middleware('auth.tsx', [middleware('role.tsx', virtualRoutes)])

const authenticatedRoutes = layout('authenticated-layout', 'authenticated/layout.tsx', [
  index('authenticated/index.tsx'),
  route('/projects', 'authenticated/projects.tsx')
])

// admin routes
const adminRoutes = layout('admin-layout', 'authenticated/(admin)/layout.tsx', [
  route('/dashboard', 'authenticated/(admin)/dashboard.tsx'),
  route('/users', 'authenticated/(admin)/user-management.tsx')
])

export const routes = rootRoute('root.tsx', [
  route('/auth', [
    layout('auth-layout', 'auth/layout.tsx', [
      route('/login-password', 'auth/login-password/route.tsx'),
      route('/login-feishu', 'auth/login-feishu/route.tsx')
    ])
  ]),
  authWithRoleMiddleware([authenticatedRoutes, adminRoutes])
])

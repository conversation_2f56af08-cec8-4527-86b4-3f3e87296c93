import { create } from 'zustand'

interface MenuState {
  openedItem: string
  isDrawerOpened: boolean

  setOpenedItem: (item: string) => void
  setDrawerOpen: (isOpen: boolean) => void
  reset: () => void
}

export const useMenuStore = create<MenuState>(set => ({
  openedItem: '',
  isDrawerOpened: false,

  setOpenedItem: item => set({ openedItem: item }),
  setDrawerOpen: isOpen => set({ isDrawerOpened: isOpen }),
  reset: () =>
    set({
      openedItem: '',
      isDrawerOpened: false
    })
}))

import type { SxProps, Theme } from '@mui/material/styles'
import { create } from 'zustand'

// -------------------- 类型定义 --------------------

export interface SnackbarOrigin {
  vertical: 'top' | 'bottom'
  horizontal: 'left' | 'center' | 'right'
}

export type SnackbarVariant = 'default' | 'error' | 'success' | 'warning' | 'info' | 'alert'

export type SnackbarTransition =
  | 'SlideLeft'
  | 'SlideUp'
  | 'SlideRight'
  | 'SlideDown'
  | 'Grow'
  | 'Zoom'
  | 'Fade'

export type SnackbarIconVariant = 'default' | 'hide' | 'useemojis'

export interface SnackbarAlertConfig {
  color: 'success' | 'info' | 'warning' | 'error'
  variant: 'filled' | 'outlined' | 'standard'
  sx?: SxProps<Theme>
}

export interface SnackbarState {
  open: boolean
  message: string
  anchorOrigin: SnackbarOrigin
  variant: SnackbarVariant
  alert: SnackbarAlertConfig
  transition: SnackbarTransition
  action: boolean
  close: boolean
  actionButton: boolean
  maxStack: number
  dense: boolean
  iconVariant: SnackbarIconVariant
  hideIconVariant: boolean

  // 修改函数
  openSnackbar: (partial: Partial<SnackbarState>) => void
  closeSnackbar: () => void
  setMaxStack: (value: number) => void
  setDense: (dense: boolean) => void
  setIconVariant: (variant: SnackbarIconVariant) => void
}

// -------------------- 初始状态 --------------------

const initialState: Omit<
  SnackbarState,
  'openSnackbar' | 'closeSnackbar' | 'setMaxStack' | 'setDense' | 'setIconVariant'
> = {
  open: false,
  action: false,
  message: 'Note archived',
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'right'
  },
  variant: 'default',
  alert: {
    color: 'warning',
    variant: 'filled'
  },
  transition: 'Zoom',
  close: false,
  actionButton: false,
  maxStack: 3,
  dense: false,
  iconVariant: 'useemojis',
  hideIconVariant: false
}

// -------------------- Zustand Store 创建 --------------------

export const useSnackbarStore = create<SnackbarState>(set => ({
  ...initialState,

  openSnackbar: partial => {
    set(state => ({
      ...state,
      ...partial,
      alert: {
        ...state.alert,
        ...(partial.alert ?? {})
      },
      open: true
    }))
  },

  closeSnackbar: () => {
    set(state => ({
      ...state,
      open: false
    }))
  },

  setMaxStack: value => {
    set(state => ({ ...state, maxStack: value }))
  },

  setDense: dense => {
    set(state => ({ ...state, dense }))
  },

  setIconVariant: variant => {
    set(state => ({
      ...state,
      iconVariant: variant,
      hideIconVariant: variant === 'hide'
    }))
  }
}))

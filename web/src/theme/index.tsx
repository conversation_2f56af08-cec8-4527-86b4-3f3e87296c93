import * as React from 'react'
import CssBaseline from '@mui/material/CssBaseline'
import GlobalStyles from '@mui/material/GlobalStyles'
import { createTheme, StyledEngineProvider, ThemeProvider } from '@mui/material/styles'
// https://mui.com/x/react-charts/quickstart/#theme-augmentation
import type {} from '@mui/x-charts/themeAugmentation'
import type {} from '@mui/x-date-pickers/AdapterDateFns'
import type {} from '@mui/x-date-pickers/themeAugmentation'

import { useConfig } from '@/contexts/config'
import componentsOverrides from './overrides'
import { palette } from './palette'
import { shadow } from './shadow'
import { typography } from './typography'

declare module '@mui/material/styles' {
  interface PaletteColor {
    lighter: string
    darker: string
  }

  interface Theme {
    customShadows: {
      button: string
      section: string
      tooltip: string
      focus: string
    }
  }
  // allow configuration using `createTheme()`
  interface ThemeOptions {
    customShadows?: {
      button?: string
      section?: string
      tooltip?: string
      focus?: string
    }
  }
}

export const ThemeCustomization = ({ children }: { children: React.ReactNode }) => {
  const { mode } = useConfig()

  const themePalette = React.useMemo(() => palette(mode), [mode])

  const themeDefault = createTheme({
    breakpoints: {
      values: {
        xs: 0,
        sm: 768,
        md: 1024,
        lg: 1266,
        xl: 1440
      }
    },
    palette: themePalette,
    customShadows: {}
  })

  // create duplicate theme due to responsive typography and fontFamily
  const theme = createTheme({
    ...themeDefault,
    typography: typography(themeDefault),
    customShadows: shadow(themeDefault)
  })
  theme.components = componentsOverrides(theme)

  return (
    <ThemeProvider theme={theme}>
      <StyledEngineProvider>
        <CssBaseline enableColorScheme />
        <GlobalStyles styles='@layer theme, base, mui, components, utilities;' />
        {children}
      </StyledEngineProvider>
    </ThemeProvider>
  )
}

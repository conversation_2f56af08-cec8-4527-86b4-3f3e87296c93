import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - AVATAR GROUP  ***************************/

export const AvatarGroup = (): Components<Theme> => {
  return {
    MuiAvatarGroup: {
      defaultProps: {
        slotProps: {
          additionalAvatar: {
            color: 'default'
          }
        }
      },
      styleOverrides: {
        avatar: { width: 32, height: 32 }
      }
    }
  }
}

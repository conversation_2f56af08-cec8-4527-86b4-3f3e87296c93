import { alpha, type Components, type Theme } from '@mui/material/styles'

/***************************  OVERRIDES - BACKDROP  ***************************/

export const Backdrop = (theme: Theme): Components<Theme> => {
  return {
    MuiBackdrop: {
      styleOverrides: {
        root: {
          backgroundColor: alpha(theme.palette.grey[900], 0.2),
          ...theme.applyStyles('dark', {
            backgroundColor: alpha(theme.palette.grey[600], 0.2)
          })
        }
      }
    }
  }
}

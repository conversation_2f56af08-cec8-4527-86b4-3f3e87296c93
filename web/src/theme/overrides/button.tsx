// @project

import type { ButtonProps } from '@mui/material/Button'
import type { Components, CSSObject, Theme } from '@mui/material/styles'

import { generateFocusStyle } from '../utils/generate-focus-style'

const colors: readonly ButtonProps['color'][] = [
  'primary',
  'secondary',
  'success',
  'error',
  'info',
  'warning'
]

/***************************  OVERRIDES - BUTTON  ***************************/

export const Button = (theme: Theme): Components<Theme> => {
  const boxShadow = {
    'boxShadow': theme.customShadows.button,
    '&:hover': {
      boxShadow: theme.customShadows.button
    }
  }

  const textColorVariants = colors
    .map(color => {
      if (color === 'inherit') return null
      if (color === undefined) return null
      const paletteColor = theme.palette[color]
      return {
        props: { variant: 'text', color },
        style: {
          ...theme.applyStyles('dark', { color: paletteColor.light })
        }
      }
    })
    .filter(Boolean) as {
    props: { variant: 'text'; color: ButtonProps['color'] }
    style: CSSObject
  }[]

  const outlinedColorVariants = colors
    .map(color => {
      if (color === 'inherit') return null
      if (color === undefined) return null
      const paletteColor = theme.palette[color]
      return {
        props: { variant: 'outlined', color },
        style: {
          ...boxShadow,
          borderColor: paletteColor.lighter,
          ...(color === 'secondary' && {
            borderColor: theme.palette.divider,
            color: theme.palette.text.primary
          })
        }
      }
    })
    .filter(Boolean) as {
    props: { variant: 'outlined'; color: ButtonProps['color'] }
    style: CSSObject
  }[]

  return {
    MuiButton: {
      defaultProps: {
        disableFocusRipple: true
      },
      styleOverrides: {
        root: {
          'borderRadius': 8,
          '&.Mui-disabled': {
            'cursor': 'not-allowed',
            'pointerEvents': 'auto',
            '&:hover': {
              'backgroundColor': 'transparent',
              '&.MuiButton-contained': {
                backgroundColor: theme.palette.action.disabledBackground
              }
            }
          },
          '&:focus-visible': {
            ...generateFocusStyle(theme.palette.primary.main)
          },
          'variants': [
            ...textColorVariants,
            ...outlinedColorVariants,
            {
              props: { variant: 'text', color: 'secondary' },
              style: {
                color: theme.palette.text.primary
              }
            }
          ]
        },
        contained: { ...boxShadow },
        startIcon: {
          marginLeft: 0,
          marginRight: 4
        },
        endIcon: {
          marginLeft: 4
        },
        sizeSmall: {
          height: 36,
          fontSize: 12,
          lineHeight: '16px',
          letterSpacing: 0,
          padding: 10
        },
        sizeMedium: {
          height: 42,
          fontSize: 14,
          lineHeight: '18px',
          letterSpacing: 0,
          padding: 12
        }
      }
    }
  }
}

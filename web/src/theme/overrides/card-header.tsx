import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - CARD HEADER  ***************************/

export const CardHeader = (theme: Theme): Components<Theme> => {
  return {
    MuiCardHeader: {
      styleOverrides: {
        root: { padding: 20, borderBottom: `1px solid ${theme.palette.divider}` },
        action: { margin: 0 },
        content: {},
        title: { '& ~ span.MuiCardHeader-subheader': { marginTop: 4 } }
      }
    }
  }
}

import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - CHARTS AXIS  ***************************/

export const ChartsAxis = (theme: Theme): Components<Theme> => {
  return {
    MuiChartsAxis: {
      styleOverrides: {
        root: {
          '& .MuiChartsAxis-tickLabel': {
            fill: theme.palette.text.secondary
          },
          '& .MuiChartsAxis-line': {
            stroke: theme.palette.divider
          }
        }
      }
    }
  }
}

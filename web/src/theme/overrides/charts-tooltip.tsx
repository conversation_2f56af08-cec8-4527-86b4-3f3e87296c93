import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - CHARTS TOOLTIP  ***************************/

export const ChartsTooltip = (theme: Theme): Components<Theme> => {
  return {
    MuiChartsTooltip: {
      styleOverrides: {
        root: {
          '& .MuiChartsTooltip-paper': {
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: 8,
            boxShadow: theme.customShadows.section
          },
          '& .MuiChartsTooltip-row': {
            '&:first-of-type .MuiChartsTooltip-cell': { paddingTop: 14 },
            '&:last-of-type .MuiChartsTooltip-cell': { paddingBottom: 14 }
          },
          '& .MuiChartsTooltip-cell': { paddingTop: 6, paddingBottom: 6 },
          '& .<PERSON>i<PERSON>hartsTooltip-labelCell': {
            '& .MuiTypography-root': { color: theme.palette.text.secondary }
          },
          '& .Mui<PERSON>hartsTooltip-valueCell': {
            '& .MuiTypography-root': { ...theme.typography.subtitle1 }
          }
        }
      }
    }
  }
}

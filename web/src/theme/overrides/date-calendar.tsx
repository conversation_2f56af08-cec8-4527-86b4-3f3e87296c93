// @icons
import type { Theme } from '@emotion/react'
import type { Components } from '@mui/material/styles'
import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react'

/***************************  OVERRIDES - DATE CALENDAR  ***************************/

export const DateCalendar = (): Components<Theme> => {
  return {
    MuiDateCalendar: {
      defaultProps: {
        slots: {
          leftArrowIcon: IconChevronLeft,
          rightArrowIcon: IconChevronRight
        }
      }
    }
  }
}

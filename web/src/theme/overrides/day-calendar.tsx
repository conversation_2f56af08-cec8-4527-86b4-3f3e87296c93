import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - DAY CALENDAR  ***************************/

export const DayCalendar = (theme: Theme): Components<Theme> => {
  return {
    MuiDayCalendar: {
      styleOverrides: {
        weekDayLabel: {
          ...theme.typography.body1,
          color: theme.palette.text.primary
        }
      }
    }
  }
}

import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - DIALOG  ***************************/

export const Dialog = (theme: Theme): Components<Theme> => {
  return {
    MuiDialog: {
      defaultProps: {
        PaperProps: { elevation: 0 },
        closeAfterTransition: false
      },
      styleOverrides: {
        paper: {
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 16,
          margin: 8
        },
        paperFullScreen: {
          margin: 0,
          borderRadius: 0
        }
      }
    }
  }
}

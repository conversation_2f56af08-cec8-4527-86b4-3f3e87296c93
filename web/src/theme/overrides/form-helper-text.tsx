import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - FORM HELPER TEXT  ***************************/

export const FormHelperText = (theme: Theme): Components<Theme> => {
  return {
    MuiFormHelperText: {
      styleOverrides: {
        root: {
          'marginTop': 6,
          'marginLeft': 0,
          'marginRight': 0,
          'color': theme.palette.grey[700],
          '&.Mui-error': {
            color: theme.palette.error.main,
            ...theme.applyStyles('dark', { color: theme.palette.error.light })
          }
        }
      }
    }
  }
}

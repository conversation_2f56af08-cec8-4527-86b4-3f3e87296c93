import type { IconButtonProps } from '@mui/material/IconButton'
import {
  alpha,
  type Components,
  type CSSObject,
  type PaletteColor,
  type Theme
} from '@mui/material/styles'

import { generateFocusStyle } from '../utils/generate-focus-style'

const colors: readonly IconButtonProps['color'][] = [
  'primary',
  'secondary',
  'success',
  'error',
  'warning',
  'info'
]

/***************************  COMPONENT - ICON BUTTON  ***************************/

declare module '@mui/material/IconButton' {
  interface IconButtonOwnProps {
    variant?: 'outlined' | 'contained'
  }
}

export const IconButton = (theme: Theme): Components<Theme> => {
  const createColorVariant = (
    color: IconButtonProps['color'],
    variant: IconButtonProps['variant'],
    styleFn: (paletteColor: PaletteColor) => CSSObject
  ) => {
    if (color === 'inherit' || color === 'default' || color === undefined) return null
    const paletteColor = theme.palette[color]

    return {
      props: { variant, color },
      style: styleFn(paletteColor)
    }
  }

  const commonDisabledStyles = {
    '&.Mui-disabled': {
      color: theme.palette.action.disabled,
      backgroundColor: theme.palette.action.disabledBackground
    }
  }

  const colorTextVariants = colors
    .map(color =>
      createColorVariant(color, undefined, paletteColor => ({
        color: paletteColor.main,
        ...theme.applyStyles('dark', { color: paletteColor.light })
      }))
    )
    .filter(Boolean) as {
    props: { color: IconButtonProps['color'] }
    style: CSSObject
  }[]

  const colorContainedVariants = colors
    .map(color =>
      createColorVariant(color, 'contained', paletteColor => ({
        'color': paletteColor.contrastText,
        'backgroundColor': paletteColor.main,
        '&:hover': {
          backgroundColor: paletteColor.dark
        },
        ...commonDisabledStyles
      }))
    )
    .filter(Boolean) as {
    props: { variant: 'contained'; color: IconButtonProps['color'] }
    style: CSSObject
  }[]

  const colorOutlinedVariants = colors
    .map(color =>
      createColorVariant(color, 'outlined', paletteColor => ({
        'color': paletteColor.main,
        'border': `1px solid ${paletteColor.lighter}`,
        ...theme.applyStyles('dark', {
          color: paletteColor.light,
          borderColor: alpha(paletteColor.light, 0.25)
        }),
        ...(color === 'secondary' && {
          color: theme.palette.text.primary,
          borderColor: theme.palette.divider
        }),
        '&.Mui-disabled': {
          color: theme.palette.action.disabled,
          backgroundColor: alpha(theme.palette.grey[700], 0.04),
          borderColor: theme.palette.action.disabledBackground
        }
      }))
    )
    .filter(Boolean) as {
    props: { variant: 'outlined'; color: IconButtonProps['color'] }
    style: CSSObject
  }[]

  return {
    MuiIconButton: {
      defaultProps: {
        disableFocusRipple: true,
        color: 'primary'
      },
      styleOverrides: {
        root: {
          'borderRadius': 8,
          '& .MuiTouchRipple-root span': {
            borderRadius: 8
          },
          '&.Mui-disabled': {
            pointerEvents: 'auto',
            cursor: 'not-allowed'
          },
          '&:focus-visible': {
            ...generateFocusStyle(theme.palette.primary.main)
          },
          'variants': [...colorTextVariants, ...colorContainedVariants, ...colorOutlinedVariants]
        },
        sizeSmall: { width: 36, height: 36 },
        sizeMedium: { width: 42, height: 42 },
        sizeLarge: { width: 48, height: 48 }
      }
    }
  }
}

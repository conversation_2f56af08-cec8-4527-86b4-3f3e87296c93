import type { Theme } from '@mui/material/styles'
import { merge } from 'lodash-es'

import { Alert } from './alert'
import { Autocomplete } from './autocomplete'
import { Avatar } from './avatar'
import { AvatarGroup } from './avatar-group'
import { Backdrop } from './backdrop'
import { BarLabel } from './bar-label'
import { Breadcrumbs } from './breadcrumbs'
import { Button } from './button'
import { CardActions } from './card-actions'
import { CardContent } from './card-content'
import { CardHeader } from './card-header'
import { ChartsAxis } from './charts-axis'
import { ChartsAxisHighlight } from './charts-axis-highlight'
import { ChartsTooltip } from './charts-tooltip'
import Checkbox from './checkbox'
import { Chip } from './chip'
import { DateCalendar } from './date-calendar'
import { DayCalendar } from './day-calendar'
import { Dialog } from './dialog'
import { DialogActions } from './dialog-actions'
import { DialogContent } from './dialog-content'
import { DialogTitle } from './dialog-title'
import { FormControlLabel } from './form-control-label'
import { FormHelperText } from './form-helper-text'
import { IconButton } from './icon-button'
import { InputAdornment } from './input-adornment'
import { InputLabel } from './input-label'
import { LinearProgress } from './linear-progress'
import { ListItemButton } from './list-item-button'
import { ListItemIcon } from './list-item-icon'
import { ListItemText } from './list-item-text'
import { Menu } from './menu'
import { MenuItem } from './menu-item'
import { Modal } from './modal'
import { OutlinedInput } from './outlined-input'
import { Pagination } from './pagination'
import { PaginationItem } from './pagination-item'
import { PickersArrowSwitcher } from './pickers-arrow-switcher'
import { PickersCalendarHeader } from './pickers-calendar-header'
import { PickersDay } from './pickers-day'
import { PickersPopper } from './pickers-popper'
import { Popper } from './popper'
import { Radio } from './radio'
import { Slider } from './slider'
import { Switch } from './switch'
import { Tab } from './tab'
import { TableCell } from './table-cell'
import { TableHead } from './table-head'
import { TableRow } from './table-row'
import { Tabs } from './tabs'
import { Tooltip } from './tooltip'
import { Typography } from './typography'

/***************************  OVERRIDES - MAIN  ***************************/

export default function componentsOverrides(theme: Theme) {
  return merge(
    Alert(),
    Autocomplete(theme),
    Avatar(theme),
    AvatarGroup(),
    Backdrop(theme),
    BarLabel(theme),
    Breadcrumbs(theme),
    Button(theme),
    CardActions(theme),
    CardContent(),
    CardHeader(theme),
    ChartsAxis(theme),
    ChartsAxisHighlight(theme),
    ChartsTooltip(theme),
    Checkbox(theme),
    Chip(theme),
    DateCalendar(),
    DayCalendar(theme),
    Dialog(theme),
    DialogActions(),
    DialogContent(),
    DialogTitle(),
    FormControlLabel(theme),
    FormHelperText(theme),
    IconButton(theme),
    InputAdornment(theme),
    InputLabel(theme),
    LinearProgress(theme),
    ListItemButton(theme),
    ListItemIcon(),
    ListItemText(),
    Menu(theme),
    MenuItem(theme),
    Modal(),
    OutlinedInput(theme),
    Pagination(theme),
    PaginationItem(theme),
    PickersArrowSwitcher(theme),
    PickersCalendarHeader(theme),
    PickersDay(theme),
    PickersPopper(theme),
    Popper(),
    Radio(theme),
    Slider(theme),
    Switch(theme),
    Tab(theme),
    TableCell(theme),
    TableHead(theme),
    TableRow(theme),
    Tabs(theme),
    Tooltip(theme),
    Typography()
  )
}

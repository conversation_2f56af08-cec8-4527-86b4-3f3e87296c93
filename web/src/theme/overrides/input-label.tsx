import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - INPUT LABEL  ***************************/

export const InputLabel = (theme: Theme): Components<Theme> => {
  return {
    MuiInputLabel: {
      styleOverrides: {
        root: {
          ...theme.typography.body2,
          'color': theme.palette.text.primary,
          'marginBottom': 6,
          '&.Mui-error': {
            color: theme.palette.error.main
          }
        },
        asterisk: {
          color: theme.palette.error.main
        }
      }
    }
  }
}

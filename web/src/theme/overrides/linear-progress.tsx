import type { LinearProgressProps } from '@mui/material/LinearProgress'
import type { Components, PaletteColor, Theme } from '@mui/material/styles'

const colors: readonly LinearProgressProps['color'][] = [
  'primary',
  'secondary',
  'error',
  'info',
  'success',
  'warning'
]

/***************************  COMPONENT - LINEAR PROGRESS  ***************************/

declare module '@mui/material/LinearProgress' {
  interface LinearProgressProps {
    type?: 'light'
  }
}

export const LinearProgress = (theme: Theme): Components<Theme> => {
  const colorVariants = colors.map(color => {
    let paletteColor: PaletteColor
    if (color === 'inherit' || color === undefined) paletteColor = theme.palette.primary
    else paletteColor = theme.palette[color]
    return {
      props: { color },
      style: {
        '& .MuiLinearProgress-bar': {
          backgroundColor: paletteColor.main,
          ...theme.applyStyles('dark', { backgroundColor: paletteColor.light })
        }
      }
    }
  })

  return {
    MuiLinearProgress: {
      defaultProps: {
        variant: 'determinate'
      },
      styleOverrides: {
        root: {
          borderRadius: 24,
          backgroundColor: theme.palette.grey[100],
          ...theme.applyStyles('dark', { backgroundColor: theme.palette.grey[300] }),
          variants: [
            ...colorVariants,
            {
              props: { type: 'light' },
              style: {
                '& .MuiLinearProgress-bar': {
                  opacity: 0.6
                }
              }
            }
          ]
        },
        bar: {
          borderRadius: 24
        }
      }
    }
  }
}

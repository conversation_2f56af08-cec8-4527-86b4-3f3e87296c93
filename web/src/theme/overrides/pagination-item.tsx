// @assets
import type { Components, Theme } from '@mui/material/styles'
import { IconArrowLeft, IconArrowRight } from '@tabler/icons-react'

/***************************  OVERRIDES - PAGINATION ITEM  ***************************/

declare module '@mui/material/PaginationItem' {
  interface PaginationItemClasses {
    sizeMedium: true
  }
}

export const PaginationItem = (theme: Theme): Components<Theme> => {
  return {
    MuiPaginationItem: {
      defaultProps: {
        slots: { previous: IconArrowLeft, next: IconArrowRight },
        sx: { svg: { width: 16, height: 16 } }
      },
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: 0,
          ...theme.typography.caption1
        },
        sizeSmall: { width: 32, height: 32 },
        sizeMedium: { width: 42, height: 42 },
        sizeLarge: { width: 48, height: 18 },
        ellipsis: { height: 18 },
        text: {
          color: theme.palette.text.secondary
        }
      }
    }
  }
}

import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - PICKERS ARROW SWITCHER  ***************************/

export const PickersArrowSwitcher = (theme: Theme): Components<Theme> => {
  return {
    MuiPickersArrowSwitcher: {
      styleOverrides: {
        root: {
          '& .MuiPickersArrowSwitcher-button': { height: 36, width: 36, padding: 10 },
          'svg': { width: 16, height: 16, color: theme.palette.text.secondary }
        }
      }
    }
  }
}

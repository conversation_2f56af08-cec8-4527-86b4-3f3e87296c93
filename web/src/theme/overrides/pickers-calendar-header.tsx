import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - PICKERS CALENDAR HEADER  ***************************/

export const PickersCalendarHeader = (theme: Theme): Components<Theme> => {
  return {
    MuiPickersCalendarHeader: {
      styleOverrides: {
        root: {
          '& .MuiPickersCalendarHeader-switchViewIcon': {
            fill: theme.palette.text.secondary
          }
        },
        label: {
          ...theme.typography.subtitle2
        }
      }
    }
  }
}

import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - PICKERS DAY  ***************************/

export const PickersDay = (theme: Theme): Components<Theme> => {
  return {
    MuiPickersDay: {
      styleOverrides: {
        root: {
          ...theme.typography.body1,
          'color': theme.palette.grey[700],
          ':not(.Mui-selected)': {
            borderColor: theme.palette.primary.main
          }
        },
        today: {
          color: theme.palette.primary.main
        }
      }
    }
  }
}

import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - PICKERS POPPER  ***************************/

export const PickersPopper = (theme: Theme): Components<Theme> => {
  return {
    MuiPickerPopper: {
      defaultProps: {
        // slotProps: { paper: { elevation: 0 } }
      },
      styleOverrides: {
        root: {
          '& .MuiPaper-root': {
            borderRadius: 8,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: theme.customShadows.tooltip,
            backgroundImage: 'none'
          }
        }
      }
    }
  }
}

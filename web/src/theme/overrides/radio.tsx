import Box from '@mui/material/Box'
import type { RadioProps } from '@mui/material/Radio'
import type { Components, CSSObject, Theme } from '@mui/material/styles'

import { generateFocusStyle } from '../utils/generate-focus-style'

const colors: readonly RadioProps['color'][] = [
  'primary',
  'secondary',
  'success',
  'error',
  'warning',
  'info'
]

/***************************  OVERRIDES - RADIO  ***************************/

declare module '@mui/material/Radio' {
  interface RadioClasses {
    sizeLarge: true
  }
}

export const Radio = (theme: Theme): Components<Theme> => {
  // Generate color variants for the Radio component
  const colorVariants = colors
    .map(color => {
      if (color === undefined || color === 'default') return null
      const paletteColor = theme.palette[color]
      return {
        props: { color },
        style: {
          '& .MuiRadio-blueprint.Mui-checked': {
            borderColor: paletteColor.main,
            ...theme.applyStyles('dark', { borderColor: paletteColor.light })
          }
        }
      }
    })
    .filter(Boolean) as {
    props: { color: RadioProps['color'] }
    style: CSSObject
  }[]

  return {
    MuiRadio: {
      defaultProps: {
        disableFocusRipple: true,
        icon: (
          <Box
            className='MuiRadio-blueprint'
            sx={{
              border: `1px solid ${theme.palette.grey[500]}`,
              ...theme.applyStyles('dark', { borderColor: theme.palette.grey[700] })
            }}
          />
        ),
        checkedIcon: (
          <Box
            className='MuiRadio-blueprint Mui-checked'
            sx={{ border: `4px solid ${theme.palette.primary.main}` }}
          />
        )
      },
      styleOverrides: {
        root: {
          'padding': 6,
          'marginLeft': 4,
          'marginRight': 4,
          '& .MuiRadio-blueprint': {
            borderRadius: '50%',
            borderColor: theme.palette.grey[500],
            ...theme.applyStyles('dark', { borderColor: theme.palette.grey[700] }),
            width: 16,
            height: 16
          },
          '&:hover:not(.Mui-checked):not(.Mui-disabled) .MuiRadio-blueprint': {
            borderColor: theme.palette.grey[700]
          },
          '& ~ .MuiFormControlLabel-label': theme.typography.body2,
          '&.Mui-disabled': {
            '& .MuiRadio-blueprint, .MuiRadio-blueprint.Mui-checked': {
              borderColor: theme.palette.action.disabledBackground
            },
            'cursor': 'not-allowed',
            'pointerEvents': 'auto',
            '&:hover': {
              backgroundColor: 'transparent'
            }
          },
          '&.Mui-focusVisible': {
            '& .MuiRadio-blueprint': generateFocusStyle(theme.palette.primary.main)
          },
          'variants': [...colorVariants]
        },
        sizeSmall: {
          '& ~ .MuiFormControlLabel-label': theme.typography.caption,
          '& .MuiRadio-blueprint': { width: 14, height: 14 }
        },
        sizeLarge: {
          '& ~ .MuiFormControlLabel-label': theme.typography.body1,
          '& .MuiRadio-blueprint': {
            'width': 20,
            'height': 20,
            '&.Mui-checked': { borderWidth: 6 }
          }
        }
      }
    }
  }
}

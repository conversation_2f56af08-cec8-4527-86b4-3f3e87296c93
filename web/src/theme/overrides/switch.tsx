import type { Components, CSSObject, Theme } from '@mui/material/styles'
import type { SwitchProps } from '@mui/material/Switch'

type Size = SwitchProps['size']
const colors: readonly SwitchProps['color'][] = [
  'primary',
  'secondary',
  'success',
  'error',
  'warning',
  'info'
]

/***************************  SWITCH - SIZE  ***************************/

declare module '@mui/material/Switch' {
  interface SwitchPropsSizeOverrides {
    large: true
  }
  interface SwitchClasses {
    sizeLarge: true
  }
}

const getSizeStyle = (size: Size) => {
  switch (size) {
    case 'small':
      return { width: 34, height: 20, base: 14, thumb: 16, trackRadius: 16 }
    case 'large':
      return { width: 42, height: 24, base: 18, thumb: 20, trackRadius: 16 }
    case 'medium':
    default:
      return { width: 38, height: 22, base: 16, thumb: 18, trackRadius: 16 }
  }
}

const switchStyle = (size: Size): CSSObject => {
  const sizes = getSizeStyle(size)

  return {
    'width': sizes.width,
    'height': sizes.height,
    '& .MuiSwitch-switchBase': {
      'padding': 2,
      '&.Mui-checked': {
        transform: `translateX(${sizes.base}px)`
      }
    },
    '& .MuiSwitch-thumb': {
      width: sizes.thumb,
      height: sizes.thumb
    },
    '& .MuiSwitch-track': {
      borderRadius: sizes.trackRadius
    }
  }
}

/***************************  OVERRIDES - SWITCH  ***************************/

export const Switch = (theme: Theme): Components<Theme> => {
  const colorVariants = colors
    .map(color => {
      if (color === 'default' || color === undefined) return null
      const paletteColor = theme.palette[color]

      return {
        props: { color },
        style: {
          '& .MuiSwitch-switchBase': {
            '&.Mui-checked': {
              '& ~ .MuiSwitch-track': {
                backgroundColor: paletteColor.main,
                ...theme.applyStyles('dark', { backgroundColor: paletteColor.light })
              }
            },
            '&:not(.Mui-checked) ~ .MuiSwitch-track': {
              backgroundColor: theme.palette.secondary.lighter,
              ...theme.applyStyles('dark', { backgroundColor: theme.palette.grey[700] })
            }
          }
        }
      }
    })
    .filter(Boolean) as {
    props: { color: SwitchProps['color'] }
    style: CSSObject
  }[]

  return {
    MuiSwitch: {
      styleOverrides: {
        root: {
          color: theme.palette.text.primary,
          padding: 0,
          display: 'flex',
          ...switchStyle('medium'),
          variants: [...colorVariants]
        },
        track: {
          opacity: 1,
          backgroundColor: theme.palette.secondary.lighter,
          boxSizing: 'border-box'
        },
        thumb: {
          borderRadius: '50%',
          transition: theme.transitions.create(['width'], {
            duration: 200
          })
        },
        switchBase: {
          '&.Mui-checked': {
            'color': theme.palette.background.default,
            '& ~ .MuiSwitch-track': {
              opacity: 1
            },
            '&.Mui-disabled': {
              'color': theme.palette.background.paper,
              '~.MuiSwitch-track': {
                opacity: 0.1
              }
            }
          },
          '&.Mui-disabled': {
            'color': theme.palette.background.paper,
            '~.MuiSwitch-track': {
              opacity: 0.3
            },
            'cursor': 'not-allowed',
            'pointerEvents': 'auto',
            '&:hover': {
              backgroundColor: 'transparent'
            }
          }
        },
        sizeLarge: {
          ...switchStyle('large'),
          '& ~ .MuiFormControlLabel-label': theme.typography.body1
        },
        sizeSmall: {
          ...switchStyle('small'),
          '& ~ .MuiFormControlLabel-label': theme.typography.body2
        }
      }
    }
  }
}

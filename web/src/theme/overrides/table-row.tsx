import type { Components, Theme } from '@mui/material/styles'

/***************************  OVERRIDES - TABLE ROW  ***************************/

export const TableRow = (theme: Theme): Components<Theme> => {
  return {
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&.Mui-selected': {
            'backgroundColor': theme.palette.grey[50],
            ...theme.applyStyles('dark', { backgroundColor: theme.palette.grey[200] }),
            '&:hover': { backgroundColor: theme.palette.grey[100] }
          },
          '&:not(.Mui-selected)&.MuiTableRow-hover:hover': {
            backgroundColor: theme.palette.action.hover
          }
        }
      }
    }
  }
}

import { alpha, type Components, type Theme } from '@mui/material/styles'

const segmentedBorderRadius = 8

/***************************  OVERRIDES - TABS  ***************************/

declare module '@mui/material/Tabs' {
  interface TabsOwnProps {
    type?: 'segmented'
  }
}

export const Tabs = (theme: Theme): Components<Theme> => {
  return {
    MuiTabs: {
      defaultProps: {
        indicatorColor: 'secondary',
        textColor: 'secondary'
      },

      styleOverrides: {
        root: {
          minHeight: 42,
          variants: [
            {
              props: { type: 'segmented', variant: 'fullWidth' },
              style: {
                width: '100%'
              }
            },
            {
              props: { type: 'segmented' },
              style: {
                'display': 'inline-flex',
                'borderRadius': segmentedBorderRadius,
                'overflow': 'hidden',
                'minHeight': 38,
                '& .MuiTabs-indicator': {
                  display: 'none'
                },
                '& .MuiTab-root': {
                  ...theme.typography.body2,
                  'color': theme.palette.text.secondary,
                  'textTransform': 'none',
                  'minHeight': 38,
                  'padding': '9px 12px',
                  'borderRadius': 0,
                  'border': `1px solid ${theme.palette.grey[200]}`,
                  '&:first-of-type': {
                    borderTopLeftRadius: segmentedBorderRadius,
                    borderBottomLeftRadius: segmentedBorderRadius
                  },
                  '&:last-of-type': {
                    borderTopRightRadius: segmentedBorderRadius,
                    borderBottomRightRadius: segmentedBorderRadius
                  },
                  '&:not(:first-of-type)': {
                    borderLeft: 'none' // Prevent double border between tab
                  },
                  '&.Mui-selected': {
                    'backgroundColor': theme.palette.grey[100],
                    '&:hover': {
                      backgroundColor: theme.palette.grey[200]
                    }
                  },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.grey[200], 0.25)
                  },
                  '&.Mui-disabled': {
                    color: theme.palette.action.disabled
                  }
                },
                '& .Mui-focusVisible': {
                  'backgroundColor': alpha(theme.palette.grey[500], 0.25),
                  ...theme.applyStyles('dark', {
                    backgroundColor: alpha(theme.palette.grey[600], 0.1)
                  }),
                  '&.Mui-selected': {
                    backgroundColor: alpha(theme.palette.secondary.light, 0.5)
                  }
                }
              }
            }
          ]
        },

        indicator: {
          variants: [
            {
              props: { indicatorColor: 'secondary' },
              style: {
                backgroundColor: theme.palette.text.primary
              }
            }
          ]
        }
      }
    }
  }
}

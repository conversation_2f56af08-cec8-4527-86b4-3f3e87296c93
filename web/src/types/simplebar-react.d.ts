declare module 'simplebar-react' {
  import type { ComponentProps, ReactNode } from 'react'

  export interface SimpleBarProps extends ComponentProps<'div'> {
    children?: ReactNode
    className?: string
    style?: React.CSSProperties
    scrollableNodeProps?: object
    contentNodeProps?: object
    forceVisible?: boolean | 'x' | 'y'
    autoHide?: boolean
    clickOnTrack?: boolean
  }

  const SimpleBar: React.FC<SimpleBarProps>
  export default SimpleBar
}

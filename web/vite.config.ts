import tailwindcss from '@tailwindcss/vite'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react'
import { defineConfig, loadEnv, PluginOption } from 'vite'
import tsconfigPaths from 'vite-tsconfig-paths'

const virtualRouteFileChangeReloadPlugin: PluginOption = {
  name: 'watch-config-restart',
  configureServer(server) {
    server.watcher.add('./src/routes.ts')
    server.watcher.on('change', path => {
      if (path.endsWith('src/routes.ts')) {
        console.log('Virtual route changed')
        server.restart()
      }
    })
  }
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    build: {
      outDir: 'dist'
    },
    server: {
      host: true,
      port: 3000,
      proxy: {
        '/_api': {
          rewrite: path => path.replace(/.*\/_api/, ''),
          target: env.VITE_DEV_SERVER_URL || 'http://localhost:5000',
          changeOrigin: true,
          secure: false
        }
      }
    },
    plugins: [
      tsconfigPaths(),
      tailwindcss(),
      TanStackRouterVite({
        virtualRouteConfig: './src/routes.ts'
      }),
      react(),
      virtualRouteFileChangeReloadPlugin
    ],
    resolve: {
      alias: {
        // /esm/icons/index.mjs only exports the icons statically, so no separate chunks are created
        '@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs'
      }
    }
  }
})
